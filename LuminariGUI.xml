<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE MudletPackage>
<MudletPackage version="1.001">
	<TriggerPackage>
		<TriggerGroup isActive="yes" isFolder="yes" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
			<name>LuminariGUI</name>
			<script/>
			<triggerType>0</triggerType>
			<conditionLineDelta>0</conditionLineDelta>
			<mStayOpen>0</mStayOpen>
			<mCommand/>
			<packageName>LuminariGUI</packageName>
			<mFgColor>#ff0000</mFgColor>
			<mBgColor>#ffff00</mBgColor>
			<mSoundFile/>
			<colorTriggerFgColor>#000000</colorTriggerFgColor>
			<colorTriggerBgColor>#000000</colorTriggerBgColor>
			<regexCodeList/>
			<regexCodePropertyList/>
			<TriggerGroup isActive="yes" isFolder="yes" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
				<name>YATCOConfig</name>
				<script/>
				<triggerType>0</triggerType>
				<conditionLineDelta>0</conditionLineDelta>
				<mStayOpen>0</mStayOpen>
				<mCommand/>
				<packageName>YATCOConfig</packageName>
				<mFgColor>#ff0000</mFgColor>
				<mBgColor>#ffff00</mBgColor>
				<mSoundFile/>
				<colorTriggerFgColor>#000000</colorTriggerFgColor>
				<colorTriggerBgColor>#000000</colorTriggerBgColor>
				<regexCodeList/>
				<regexCodePropertyList/>
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Tell</name>
					<script>-- Tell Trigger: Captures private messages between players
-- Regex patterns:
-- 1. ^(\w+) tells you, '(.*)' - Captures incoming tells: player name and message
-- 2. You tell - Captures outgoing tell confirmations
demonnic.chat:append(&quot;Tell&quot;)
if GUI.toggles.gagChat == true then
  deleteLineP()
end</script>
					<triggerType>0</triggerType>
					<conditionLineDelta>39</conditionLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand/>
					<packageName/>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile/>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>^(\w+) tells you, '(.*)'</string>
						<string>You tell </string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>1</integer>
						<integer>2</integer>
					</regexCodePropertyList>
				</Trigger>
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Congrats</name>
					<script>-- Congrats Trigger: Captures congratulations channel messages
-- Regex patterns:
-- 1. ^(\w+) congrats, '(.*)' - Captures congrats messages: player name and content
-- 2. You congrat, ' - Captures outgoing congrats confirmations
demonnic.chat:append(&quot;Congrats&quot;)
if GUI.toggles.gagChat == true then
  deleteLineP()
end</script>
					<triggerType>0</triggerType>
					<conditionLineDelta>0</conditionLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand/>
					<packageName/>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile/>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>^(\w+) congrats, '(.*)'$</string>
						<string>You congrat, '(.*)'$</string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>1</integer>
						<integer>2</integer>
					</regexCodePropertyList>
				</Trigger>
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Chat</name>
					<script>demonnic.chat:append(&quot;Chat&quot;)
if GUI.toggles.gagChat == true then
  deleteLineP()
end</script>
					<triggerType>0</triggerType>
					<conditionLineDelta>39</conditionLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand/>
					<packageName/>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile/>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>^(\w+) chats, '(.*)'$</string>
						<string>You chat, '(.*)'$</string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>1</integer>
						<integer>2</integer>
					</regexCodePropertyList>
				</Trigger>
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Auction</name>
					<script>demonnic.chat:append(&quot;Auction&quot;)
if GUI.toggles.gagChat == true then
  deleteLineP()
end</script>
					<triggerType>0</triggerType>
					<conditionLineDelta>0</conditionLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand/>
					<packageName/>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile/>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>^(\w+) auctions, '(.*)'$</string>
						<string>You auction, '(.*)'$</string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>1</integer>
						<integer>2</integer>
					</regexCodePropertyList>
				</Trigger>
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Group</name>
					<script>demonnic.chat:append(&quot;Group&quot;)
if GUI.toggles.gagChat == true then
  deleteLineP()
end</script>
					<triggerType>0</triggerType>
					<conditionLineDelta>0</conditionLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand/>
					<packageName/>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile/>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>[Group]</string>
						<string>You group-say, '</string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>2</integer>
						<integer>2</integer>
					</regexCodePropertyList>
				</Trigger>
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Wiznet</name>
					<script>demonnic.chat:append(&quot;Wiz&quot;)
if GUI.toggles.gagChat == true then
  deleteLineP()
end</script>
					<triggerType>0</triggerType>
					<conditionLineDelta>0</conditionLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand/>
					<packageName/>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile/>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>[wiznet]</string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>2</integer>
					</regexCodePropertyList>
				</Trigger>
			</TriggerGroup>
			<TriggerGroup isActive="yes" isFolder="yes" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
				<name>GUI</name>
				<script/>
				<triggerType>0</triggerType>
				<conditionLineDelta>0</conditionLineDelta>
				<mStayOpen>0</mStayOpen>
				<mCommand/>
				<packageName/>
				<mFgColor>#ff0000</mFgColor>
				<mBgColor>#ffff00</mBgColor>
				<mSoundFile/>
				<colorTriggerFgColor>#000000</colorTriggerFgColor>
				<colorTriggerBgColor>#000000</colorTriggerBgColor>
				<regexCodeList/>
				<regexCodePropertyList/>
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Capture Wilderness Map</name>
					<script>deleteLine()
clearUserWindow(&quot;map.minimap&quot;)
map.container:hide()
map.minimapcontainer:show()
-- Capture 23 lines of wilderness map data (adjust based on server map size)
maplineTrig = tempLineTrigger(1,23,[[onMapLine()]])
padding = map.calcMinimapPadding()
-- Map Line Processing Handler
-- Processes individual lines of ASCII map data and handles map display completion
-- Enhanced with comprehensive error handling for robust map processing
function onMapLine()
  -- Validate that we have a valid line to process
  if not line or line == "" then
    print("Warning: Empty or invalid map line received")
    return
  end
  
  -- Check for map completion marker
  local isLastLine = string.findPattern(line, "&lt;/WILDERNESS_MAP&gt;")
  if isLastLine then
    deleteLine()
    -- Safely cleanup trigger with validation
    if maplineTrig and exists(maplineTrig, "trigger") ~= 0 then
      killTrigger(maplineTrig)
    end
    maplineTrig = nil
    return
  end
  
  -- Process map line with error handling
  local success, err = pcall(function ()
    selectCurrentLine()
    copy()
    
    -- Validate map minimap exists before echoing
    if map.minimap and map.minimap.echo then
      -- Add padding with validation
      if padding and type(padding) == "number" then
        for i = 1, math.max(0, math.min(padding, 50)) do -- Cap padding to prevent infinite loops
          map.minimap:echo(" ")
        end
      end
      
      -- Adjust font size with error handling
      if map.adjustMinimapFontSize then
        map.adjustMinimapFontSize()
      end
      
      -- Append to buffer with validation
      if exists("map.minimap", "miniconsole") ~= 0 then
        appendBuffer("map.minimap")
      else
        print("Warning: map.minimap buffer not available")
      end
    else
      print("Warning: map.minimap not available for line processing")
    end
    
    deleteLine()
  end )
  
  -- Handle processing errors gracefully
  if not success then
    print("Error processing map line: " .. tostring(err))
    deleteLine() -- Still remove the problematic line
    -- Emergency cleanup if processing fails repeatedly
    if maplineTrig and exists(maplineTrig, "trigger") ~= 0 then
      killTrigger(maplineTrig)
      maplineTrig = nil
      print("Emergency cleanup: Map line trigger killed due to processing errors")
    end
  end
end</script>
					<triggerType>0</triggerType>
					<conditionLineDelta>0</conditionLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand/>
					<packageName/>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile/>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>&lt;WILDERNESS_MAP&gt;</string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>2</integer>
					</regexCodePropertyList>
				</Trigger>
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Capture Room Map</name>
					<script>deleteLine()
clearUserWindow(&quot;map.minimap&quot;)
-- Simple direct map mode switching based on current mode
if GUI.buttonWindow.mudletOrAscii == &quot;ASCII&quot; then
  map.container:hide()
  map.minimapcontainer:show()
elseif GUI.buttonWindow.mudletOrAscii == &quot;Mudlet&quot; then
  map.container:show()
  map.minimapcontainer:hide()
end
map.minimap:echo(&quot;\n&quot;)
-- Capture 11 lines of room map data (smaller than wilderness maps)
maplineTrig = tempLineTrigger(1,11,[[onRoomMapLine()]])
padding = map.calcAsciimapPadding()
-- Room Map Line Processing Handler
-- Processes individual lines of room-specific ASCII map data
-- Enhanced with comprehensive error handling for robust map processing
function onRoomMapLine()
  -- Validate that we have a valid line to process
  if not line or line == "" then
    print("Warning: Empty or invalid room map line received")
    return
  end
  
  -- Check for room map completion marker
  local isLastLine = string.findPattern(line, "&lt;/ROOM_MAP&gt;")
  if isLastLine then
    deleteLine()
    -- Safely cleanup trigger with validation
    if maplineTrig and exists(maplineTrig, "trigger") ~= 0 then
      killTrigger(maplineTrig)
    end
    maplineTrig = nil
    return
  end
  
  -- Process room map line with error handling
  local success, err = pcall(function ()
    selectCurrentLine()
    copy()
    
    -- Validate map minimap exists before echoing
    if map.minimap and map.minimap.echo then
      map.minimap:echo(" ")
      
      -- Adjust ASCII map font size with error handling
      if map.adjustAsciimapFontSize then
        map.adjustAsciimapFontSize()
      end
      
      -- Append to buffer with validation
      if exists("map.minimap", "miniconsole") ~= 0 then
        appendBuffer("map.minimap")
      else
        print("Warning: map.minimap buffer not available for room map")
      end
    else
      print("Warning: map.minimap not available for room map line processing")
    end
    
    deleteLine()
  end )
  
  -- Handle processing errors gracefully
  if not success then
    print("Error processing room map line: " .. tostring(err))
    deleteLine() -- Still remove the problematic line
    -- Emergency cleanup if processing fails repeatedly
    if maplineTrig and exists(maplineTrig, "trigger") ~= 0 then
      killTrigger(maplineTrig)
      maplineTrig = nil
      print("Emergency cleanup: Room map line trigger killed due to processing errors")
    end
  end
end</script>
					<triggerType>0</triggerType>
					<conditionLineDelta>0</conditionLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand/>
					<packageName/>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile/>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>&lt;ROOM_MAP&gt;</string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>2</integer>
					</regexCodePropertyList>
				</Trigger>
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Gag blank lines</name>
					<script>deleteLine()</script>
					<triggerType>0</triggerType>
					<conditionLineDelta>0</conditionLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand/>
					<packageName/>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile/>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>^$</string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>1</integer>
					</regexCodePropertyList>
				</Trigger>
				<TriggerGroup isActive="yes" isFolder="yes" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Cast Console</name>
					<script/>
					<triggerType>0</triggerType>
					<conditionLineDelta>0</conditionLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand/>
					<packageName/>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile/>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList/>
					<regexCodePropertyList/>
					<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
						<name>Started Cast</name>
						<script>GUI.castConsole_startCast(matches[2], matches[3])</script>
						<triggerType>0</triggerType>
						<conditionLineDelta>0</conditionLineDelta>
						<mStayOpen>0</mStayOpen>
						<mCommand/>
						<packageName/>
						<mFgColor>#ff0000</mFgColor>
						<mBgColor>#ffff00</mBgColor>
						<mSoundFile/>
						<colorTriggerFgColor>#000000</colorTriggerFgColor>
						<colorTriggerBgColor>#000000</colorTriggerBgColor>
						<regexCodeList>
							<string>^Casting\: (.+) (\*+)$</string>
						</regexCodeList>
						<regexCodePropertyList>
							<integer>1</integer>
						</regexCodePropertyList>
					</Trigger>
					<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
						<name>Cast Complete</name>
						<script>GUI.castConsole_completeCast()</script>
						<triggerType>0</triggerType>
						<conditionLineDelta>0</conditionLineDelta>
						<mStayOpen>0</mStayOpen>
						<mCommand/>
						<packageName/>
						<mFgColor>#ff0000</mFgColor>
						<mBgColor>#ffff00</mBgColor>
						<mSoundFile/>
						<colorTriggerFgColor>#000000</colorTriggerFgColor>
						<colorTriggerBgColor>#000000</colorTriggerBgColor>
						<regexCodeList>
							<string>You complete your spell...</string>
						</regexCodeList>
						<regexCodePropertyList>
							<integer>2</integer>
						</regexCodePropertyList>
					</Trigger>
					<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
						<name>Cast Aborted</name>
						<script>GUI.castConsole_abortedCast()</script>
						<triggerType>0</triggerType>
						<conditionLineDelta>0</conditionLineDelta>
						<mStayOpen>0</mStayOpen>
						<mCommand/>
						<packageName/>
						<mFgColor>#ff0000</mFgColor>
						<mBgColor>#ffff00</mBgColor>
						<mSoundFile/>
						<colorTriggerFgColor>#000000</colorTriggerFgColor>
						<colorTriggerBgColor>#000000</colorTriggerBgColor>
						<regexCodeList>
							<string>You abort your spell.</string>
							<string>Your spell is aborted!</string>
							<string>You are unable to find the target for your spell!</string>
							<string>You are unable to find the object for your spell!</string>
							<string>You are unable to continue your spell in your current position!</string>
						</regexCodeList>
						<regexCodePropertyList>
							<integer>3</integer>
							<integer>3</integer>
							<integer>3</integer>
							<integer>3</integer>
							<integer>3</integer>
						</regexCodePropertyList>
					</Trigger>
					<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
						<name>Cast Canceled</name>
						<script>GUI.castConsole_canceledCast()</script>
						<triggerType>0</triggerType>
						<conditionLineDelta>0</conditionLineDelta>
						<mStayOpen>0</mStayOpen>
						<mCommand/>
						<packageName/>
						<mFgColor>#ff0000</mFgColor>
						<mBgColor>#ffff00</mBgColor>
						<mSoundFile/>
						<colorTriggerFgColor>#000000</colorTriggerFgColor>
						<colorTriggerBgColor>#000000</colorTriggerBgColor>
						<regexCodeList>
							<string>You are unable to continue casting!</string>
							<string>You are too nauseated to continue casting!</string>
						</regexCodeList>
						<regexCodePropertyList>
							<integer>3</integer>
							<integer>3</integer>
						</regexCodePropertyList>
					</Trigger>
				</TriggerGroup>
			</TriggerGroup>
		</TriggerGroup>
	</TriggerPackage>
	<TimerPackage/>
	<AliasPackage>
		<AliasGroup isActive="yes" isFolder="yes">
			<name>LuminariGUI</name>
			<script/>
			<command/>
			<packageName>LuminariGUI</packageName>
			<regex/>
			<AliasGroup isActive="yes" isFolder="yes">
				<name>Toggles</name>
				<script/>
				<command/>
				<packageName/>
				<regex/>
				<Alias isActive="yes" isFolder="no">
					<name>Gag Chat</name>
					<script>--[[Toggling this will determine if the line and follow up
    prompt are removed from the main buffer for chat.]]
GUI.gagChatToggle()</script>
					<command/>
					<packageName/>
					<regex>^gag chat$</regex>
				</Alias>
				<Alias isActive="yes" isFolder="no">
					<name>Show Self</name>
					<script>--Toggles whether or not you are shown in the group tab.
GUI.showSelfToggle()</script>
					<command/>
					<packageName/>
					<regex>^show self$</regex>
				</Alias>
			</AliasGroup>
			<AliasGroup isActive="yes" isFolder="yes">
				<name>YATCO</name>
				<script/>
				<command/>
				<packageName>YATCO</packageName>
				<regex/>
				<AliasGroup isActive="yes" isFolder="yes">
					<name>Demonnic</name>
					<script/>
					<command/>
					<packageName/>
					<regex/>
					<AliasGroup isActive="yes" isFolder="yes">
						<name>Shared</name>
						<script/>
						<command/>
						<packageName/>
						<regex/>
						<Alias isActive="yes" isFolder="no">
							<name>Reset chasing</name>
							<script>demonnic.chaser:reset()</script>
							<command/>
							<packageName/>
							<regex>^chaseres$</regex>
						</Alias>
						<Alias isActive="yes" isFolder="no">
							<name>Debug</name>
							<script>if matches[2] then
  demonnic:listCategories()
else
  demonnic:toggleDebug()
end</script>
							<command/>
							<packageName/>
							<regex>^debug(?: (list))?$</regex>
						</Alias>
						<Alias isActive="yes" isFolder="no">
							<name>debug categories</name>
							<script>if matches[2] then
  demonnic:watchCategory( matches[2] )
else
  demonnic:listCategories()
end</script>
							<command/>
							<packageName/>
							<regex>^debugc(?: (.*))?$</regex>
						</Alias>
					</AliasGroup>
					<AliasGroup isActive="yes" isFolder="yes">
						<name>Tabbed Chat</name>
						<script/>
						<command/>
						<packageName/>
						<regex/>
						<Alias isActive="yes" isFolder="no">
							<name>Toggle blinking (temporary change)</name>
							<script>if demonnic.chat.config.blink then
  demonnic.chat.config.blink = false
  demonnic.chat.tabsToBlink = {}
  demonnic:echo(&quot;Blinking temporarily turned &lt;red&gt;off&lt;grey&gt;. It will reset if you edit your tabbed chat configuration, or close and reopen mudlet. To make it permanent, change demonnic.chat.config.blink to false in \&quot;Demonnic-&gt;Tabbed Chat-&gt;Configuration options\&quot; under scripts\n&quot;)
else
  demonnic.chat.config.blink = true
  demonnic.chat:blink()
  demonnic:echo(&quot;Blinking temporarily turned &lt;red&gt;on&lt;grey&gt;. It will reset if you edit your tabbed chat configuration, or close and reopen mudlet. To make it permanent, change demonnic.chat.config.blink to true in \&quot;Demonnic-&gt;Tabbed Chat-&gt;Configuration options\&quot; under scripts\n&quot;)
end</script>
							<command/>
							<packageName/>
							<regex>^dblink$</regex>
						</Alias>
						<Alias isActive="yes" isFolder="no">
							<name>fixChat</name>
							<script>local currentsetting = demonnic.chat.config.location
local newsetting = &quot;&quot;
if currentsetting == &quot;topright&quot; then
  newsetting = &quot;bottomleft&quot;
elseif currentsetting == &quot;topleft&quot; then
  newsetting = &quot;bottomright&quot;
elseif currentsetting == &quot;bottomleft&quot; then
  newsetting = &quot;topright&quot;
elseif currentsetting == &quot;bottomright&quot; then
  newsetting = &quot;topleft&quot;
end
demonnic.chat.config.location = newsetting
demonnic.chat:create()
demonnic.chat.config.location = currentsetting
demonnic.chat:create()</script>
							<command/>
							<packageName/>
							<regex>^fixchat$</regex>
						</Alias>
					</AliasGroup>
				</AliasGroup>
			</AliasGroup>
		</AliasGroup>
	</AliasPackage>
	<ActionPackage/>
	<ScriptPackage>
		<ScriptGroup isActive="yes" isFolder="yes">
			<name>LuminariGUI</name>
			<packageName>LuminariGUI</packageName>
			<script/>
			<eventHandlerList/>
			<ScriptGroup isActive="yes" isFolder="yes">
				<name>MSDPMapper</name>
				<packageName>Generic Mapper</packageName>
				<script/>
				<eventHandlerList/>
				<Script isActive="yes" isFolder="no">
					<name>MSDPMapper</name>
					<packageName/>
					<script>mudlet = mudlet or {}
mudlet.mapper_script = true
map = map or {}
map.room_info = map.room_info or {}
map.prev_info = map.prev_info or {}
map.aliases = map.aliases or {}
map.enabled = false
map.minimap_font_size = 8
map.minimap_width = 21
map.minimap_height = 21
-- ASCII map dimensions for consistent room map display
map.asciimap_width = 20
map.asciimap_height = 11
local defaults = {
  -- using Geyser to handle the mapper in this, since this is a totally new script
  -- Synchronized with GUI.Right positioning (x = &quot;-25%&quot;, y = &quot;-50%&quot;, width = &quot;25%&quot;, height = &quot;50%&quot;)
  mapper = {x = &quot;-25%&quot;, y = &quot;-50%&quot;, width = &quot;25%&quot;, height = &quot;50%&quot;}
}
--[[
  Terrain Type Color Configuration
  ===============================
  Maps Luminari MUD terrain types to visual colors in the mapper.
  Each terrain gets a unique ID and RGB color values for visual distinction.
  Configuration Rules:
  - Each terrain must have a unique ID (1-255 range recommended)
  - RGB values range from 0-255
  - Terrain types not listed here use mapper default colors
  - ID values are offset by +16 when applied to avoid conflicts with Mudlet defaults
  Adding New Terrains:
  1. Add entry with format: [&quot;Terrain Name&quot;] = {id = unique_number, r = red, g = green, b = blue}
  2. Ensure ID doesn't conflict with existing entries
  3. Choose colors that provide good contrast and visibility
  Performance: This table is read during room creation - keep it reasonably sized
]]
local terrain_types = {
  -- Indoor/Structural terrains - neutral/gray tones
  [&quot;Inside&quot;]              = {id = 1, r = 130, g = 130, b = 130},
  [&quot;City&quot;]                = {id = 2, r = 200, g = 200, b = 200},
  [&quot;Cave&quot;]                = {id = 30, r = 80, g = 80, b = 80},
  -- Natural outdoor terrains - earth tones and naturals
  [&quot;Field&quot;]               = {id = 3, r = 0, g = 170, b = 0},
  [&quot;Forest&quot;]              = {id = 4, r = 0, g = 122, b = 0},
  [&quot;Hills&quot;]               = {id = 5, r = 122, g = 69, b = 0},
  [&quot;Low Mountains&quot;]       = {id = 6, r = 100, g = 100, b = 100},
  [&quot;High Mountains&quot;]      = {id = 18, r = 255, g = 255, b = 255},
  [&quot;Desert&quot;]              = {id = 15, r = 234, g = 219, b = 124},
  [&quot;Jungle&quot;]              = {id = 31, r = 21, g = 132, b = 101},
  [&quot;Tundra&quot;]              = {id = 32, r = 224, g = 224, b = 224},
  [&quot;Taiga&quot;]               = {id = 33, r = 103, g = 137, b = 104},
  [&quot;Beach&quot;]               = {id = 34, r = 239, g = 235, b = 0},
  [&quot;Marshland&quot;]           = {id = 17, r = 81, g = 47, b = 109},
  -- Water terrains - blue spectrum
  [&quot;Water (Swim)&quot;]        = {id = 7, r = 0, g = 0, b = 255},
  [&quot;Water (No Swim)&quot;]     = {id = 8, r = 0, g = 0, b = 130},
  [&quot;Ocean&quot;]               = {id = 16, r = 0, g = 90, b = 90},
  [&quot;Underwater&quot;]          = {id = 10, r = 43, g = 43, b = 124},
  -- Aerial and special movement
  [&quot;In Flight&quot;]           = {id = 9, r = 200, g = 200, b = 255},
  -- Special/Magical locations
  [&quot;Zone Entrance&quot;]       = {id = 211, r = 255, g = 0, b = 0},     -- Bright red for visibility
  [&quot;Outer Planes&quot;]        = {id = 19, r = 168, g = 42, b = 138},
  [&quot;Lava&quot;]                = {id = 26, r = 255, g = 119, b = 0},    -- Orange-red for danger
  -- Road system - brown/tan theme for consistency
  [&quot;Road North-South&quot;]    = {id = 12, r = 119, g = 101, b = 86},
  [&quot;Road East-West&quot;]      = {id = 13, r = 119, g = 101, b = 86},
  [&quot;Road Intersection&quot;]   = {id = 14, r = 119, g = 101, b = 86},
  [&quot;Dirt Road North-South&quot;] = {id = 27, r = 142, g = 85, b = 0},
  [&quot;Dirt Road East-West&quot;] = {id = 28, r = 142, g = 85, b = 0},
  [&quot;Dirt Road Intersection&quot;] = {id = 29, r = 142, g = 85, b = 0},
  -- Underdark terrain variants - darker, muted versions of surface equivalents
  [&quot;Underdark - Wild&quot;]    = {id = 20, r = 131, g = 110, b = 145},
  [&quot;Underdark - City&quot;]    = {id = 21, r = 183, g = 178, b = 186},
  [&quot;Underdark - Inside&quot;]  = {id = 22, r = 132, g = 132, b = 132},
  [&quot;Underdark - Water (Swim)&quot;] = {id = 23, r = 70, g = 139, b = 175},
  [&quot;Underdark - Water (No Swim)&quot;] = {id = 24, r = 34, g = 68, b = 86},
  [&quot;Underdark - In Flight&quot;] = {id = 25, r = 158, g = 178, b = 188},
}
-- Movement vector calculations for 3D coordinate system
-- Used to determine relative positioning when creating connected rooms
-- Format: {x_offset, y_offset, z_offset} where positive values indicate:
-- x: east direction, y: north direction, z: up direction
local move_vectors = {
  -- Cardinal directions (horizontal plane, z=0)
  north = {0,1,0}, south = {0,-1,0}, east = {1,0,0}, west = {-1,0,0},
  -- Diagonal directions (horizontal plane, z=0)
  northwest = {-1,1,0}, northeast = {1,1,0}, southwest = {-1,-1,0}, southeast = {1,-1,0},
  -- Vertical movement
  up = {0,0,1}, down = {0,0,-1}
}
-- Direction abbreviation expansion table
-- Converts common short-form directions to full direction names
-- Used for user input processing and command parsing
local exits = {
  -- Cardinal abbreviations
  n = &quot;north&quot;, s = &quot;south&quot;, w = &quot;west&quot;, e = &quot;east&quot;,
  -- Diagonal abbreviations
  nw = &quot;northwest&quot;, ne = &quot;northeast&quot;, sw = &quot;southwest&quot;, se = &quot;southeast&quot;,
  -- Vertical abbreviations
  u = &quot;up&quot;, d = &quot;down&quot;
}
--[[
  Bidirectional Exit Mapping System
  =================================
  Maps between direction names and Mudlet's internal exit numbering system.
  This table serves dual purposes:
  1. String to number: exitmap[&quot;north&quot;] = 1
  2. Number to string: exitmap[1] = &quot;north&quot;
  Mudlet Exit Number Standards:
  - 1-8: Standard compass directions
  - 9-10: Vertical movement (up/down)
  - 11-12: Special exits (in/out)
  Used extensively in exit creation and pathfinding algorithms.
]]
local exitmap = {
  -- Direction name to number mapping
  north = 1,      northeast = 2,      northwest = 3,      east = 4,
  west = 5,       south = 6,          southeast = 7,      southwest = 8,
  up = 9,         down = 10,          [&quot;in&quot;] = 11,        out = 12,
  -- Number to direction name mapping (reverse lookup)
  [1] = &quot;north&quot;,  [2] = &quot;northeast&quot;,  [3] = &quot;northwest&quot;,  [4] = &quot;east&quot;,
  [5] = &quot;west&quot;,   [6] = &quot;south&quot;,      [7] = &quot;southeast&quot;,  [8] = &quot;southwest&quot;,
  [9] = &quot;up&quot;,     [10] = &quot;down&quot;,      [11] = &quot;in&quot;,        [12] = &quot;out&quot;,
}
--[[
  Room Creation Algorithm
  =======================
  Creates a new room in the Mudlet mapper based on MSDP room data.
  Handles coordinate calculation, area management, terrain assignment, and exit linking.
  Algorithm Overview:
  1. Create room with VNUM from MSDP data
  2. Determine area (create if new)
  3. Calculate coordinates:
     - New area: Place at origin (0,0,0)
     - Existing area: Calculate from previous room + movement vector
  4. Handle coordinate conflicts with map stretching
  5. Apply terrain colors
  6. Link exits between rooms
  Performance Considerations:
  - Map stretching algorithm is O(n) where n = rooms in area
  - Can be expensive for large areas with many coordinate conflicts
  - Consider optimizing for heavily traveled areas
  Error Handling:
  - Falls back to exit stubs if room linking fails
  - Gracefully handles missing terrain types
  - Validates movement vectors before applying
]]
local function make_room()
  local info = map.room_info
  local coords = {0,0,0}  -- Default coordinates for new areas
  -- Step 1: Create the room in Mudlet's mapper
  addRoom(info.VNUM)
  -- Step 2: Area Management - get or create area
  local areas = getAreaTable()
  local areaID = areas[info.AREA]
  if not areaID then
    -- New area - create it and use default coordinates
    areaID = addAreaName(info.AREA)
  else
    -- Existing area - calculate coordinates based on movement from previous room
    coords = {getRoomCoordinates(map.prev_info.VNUM)}
    local shift = {0,0,0}  -- Movement vector from previous to current room
    -- Find the direction we moved from previous room to current room
    for k,v in pairs(map.prev_info.EXITS) do
      if v == info.VNUM and move_vectors[k] then
        shift = move_vectors[k]
        break
      end
    end
    -- Apply movement vector to get new coordinates
    for n = 1,3 do
      coords[n] = coords[n] + shift[n]
    end
    --[[
      Map Stretching Algorithm
      ========================
      Prevents room overlap by shifting existing rooms when coordinate conflicts occur.
      This maintains spatial relationships while avoiding visual room stacking.
      Algorithm:
      1. Check if calculated coordinates are already occupied
      2. If occupied, shift ALL rooms in the direction of movement
      3. Only shift rooms that are &quot;ahead&quot; of the conflict point
      Performance Warning: This is O(n) for each room creation in contested areas.
      Consider caching room coordinates if performance becomes an issue.
    ]]
    local overlap = getRoomsByPosition(areaID,coords[1],coords[2],coords[3])
    if not table.is_empty(overlap) then
      local rooms = getAreaRooms(areaID)
      local rcoords
      -- Shift all affected rooms to prevent overlap
      for _,id in ipairs(rooms) do
        local x, y, z = getRoomCoordinates(id)
        rcoords = {x, y, z}
        -- Only shift rooms in the direction of movement
        for n = 1,3 do
          -- Check if room is &quot;ahead&quot; in the movement direction
          if shift[n] ~= 0 and (rcoords[n] - coords[n]) * shift[n] &gt;= 0 then
            rcoords[n] = rcoords[n] + shift[n]
          end
        end
        setRoomCoordinates(id,rcoords[1],rcoords[2],rcoords[3])
      end
    end
  end
  -- Step 3: Apply room properties
  setRoomArea(info.VNUM, areaID)
  setRoomCoordinates(info.VNUM, coords[1], coords[2], coords[3])
  -- Step 4: Set terrain-based visual environment (+16 offset avoids Mudlet conflicts)
  if terrain_types[info.TERRAIN] then
    setRoomEnv(info.VNUM, terrain_types[info.TERRAIN].id + 16)
  end
  -- Step 5: Link exits from previous room (if we moved from somewhere)
  if map.prev_info then -- Check if you moved into here from another room.
    local prev_exits = getRoomExits(map.prev_info.VNUM)
    for dir, id in pairs(map.prev_info.EXITS) do
      -- Only create exit if it doesn't already exist
      if prev_exits[dir] == nil then
        -- Try to create bidirectional exit, fall back to stub if target room doesn't exist
        if not setExit(map.prev_info.VNUM, id, exitmap[dir]) then
          setExitStub(map.prev_info.VNUM, exitmap[dir], true)
        end
      end
    end
  end
  -- Step 6: Create exits from current room to connected rooms
  for dir, id in pairs(info.EXITS) do
    -- TODO: Implement special exit handling for non-standard movement
    -- Future Enhancement: Add support for custom movement commands (climb, swim, etc.)
    -- Try to create exit, fall back to stub if target room doesn't exist yet
    if not setExit(info.VNUM, id, exitmap[dir]) then
      setExitStub(info.VNUM, exitmap[dir], true)
    end
  end
end
--[[
  Room Coordinate Manipulation
  ============================
  Manually shifts a room's position in the specified direction.
  Used for map corrections when automatic positioning fails or user wants manual control.
  Parameters:
    dir (string) - Direction to shift room (must exist in move_vectors table)
  Behavior:
  - Gets current room coordinates from Mudlet mapper
  - Applies movement vector for specified direction
  - Updates room position and refreshes map display
  Use Cases:
  - Correcting misaligned rooms after area exploration
  - Manual map layout adjustments
  - Fixing coordinate conflicts not resolved by map stretching
  Note: This only moves the current room - doesn't update connected rooms or exits.
  Consider using this carefully to avoid breaking spatial relationships.
]]
local function shift_room(dir)
  local ID = map.room_info.VNUM  -- Current room VNUM
  local x,y,z = getRoomCoordinates(ID)  -- Get current position
  local vector = move_vectors[dir]    -- Get movement vector for direction
  -- Apply movement vector to current coordinates
  x = x + vector[1]
  y = y + vector[2]
  z = z + vector[3]
  -- Update room position and refresh map display
  setRoomCoordinates(ID,x,y,z)
  updateMap()  -- Force map redraw to show new position
end
--[[
  Speedwalk Timeout Handler
  =========================
  Called when speedwalk movement takes too long to complete.
  Provides user feedback and safely terminates the speedwalk sequence.
  Safety Features:
  - Prevents infinite speedwalk loops
  - Clears speedwalk state to avoid stuck conditions
  - Provides clear error messaging to user
]]
local function speedwalk_timeout()
  speedwalk_active = false  -- Disable speedwalk system
  cecho(&quot;&lt;red&gt;SpeedWalk failed to reach destination!\n&quot;)
end
--[[
  Speedwalk Movement Controller
  =============================
  Core pathfinding logic that manages automated movement between rooms.
  Handles movement validation, path progression, and destination checking.
  Algorithm Flow:
  1. Initialize speedwalk state variables
  2. Validate movement resources (movement points)
  3. Check path completion conditions
  4. Execute next movement step with timeout protection
  Movement Validation:
  - Requires minimum 10 movement points to continue
  - Validates path exists and destination is reachable
  - Handles arrival detection and state cleanup
  Performance Considerations:
  - Uses 10-second timeout to prevent infinite loops
  - TODO: Implement terrain-based movement cost calculations
  - Future Enhancement: Add movement point costs for different terrain types
  - Could be enhanced with movement point prediction
  Error Recovery:
  - Gracefully handles insufficient movement points
  - Provides user feedback for all termination conditions
  - Timeout protection prevents system lockup
]]
local function speedwalk_check()
  -- Initialize speedwalk state (defensive programming for global variables)
  speedwalk_active = speedwalk_active or false
  speedwalk_timer = speedwalk_timer or false
  -- Clear any existing timeout timer
  if speedwalk_timer then killTimer(speedwalk_timer) end
  -- Movement Resource Validation
  -- TODO: Make this more versatile, consider movement cost of terrain type
  -- Future Enhancement: Implement weighted pathfinding based on terrain difficulty
  local move = tonumber(msdp.MOVEMENT or &quot;0&quot;)
  if move &lt; 10 then
    cecho(&quot;&lt;red&gt;SpeedWalk: &lt;white&gt;Not enough movement to continue SpeedWalk!\n&quot;)
    return
  end
  -- Path Completion Checks
  if #speedWalkDir &lt; 1 then
    speedwalk_active = false  -- No more directions to follow
    return
  end
  -- Destination Arrival Check
  if speedWalkPath[# speedWalkPath] == msdp.ROOM.VNUM then
    speedwalk_active = false
    cecho(&quot;&lt;red&gt;SpeedWalk: &lt;white&gt;You have arrived at your destination.\n&quot;)
    return
  end
  -- Safety check - ensure speedwalk is still active
  if not speedwalk_active then return end
  -- Set timeout protection (10 seconds per movement step)
  if GUI and GUI.createTimer then
    speedwalk_timer = GUI.createTimer(10, function () speedwalk_timeout() end, false, &quot;speedwalk_timeout&quot;)
  else
    speedwalk_timer = tempTimer(10, function () speedwalk_timeout() end)
  end
  -- Determine next movement step based on current room position in path
  local step_n = speedwalk_vnums[msdp.ROOM.VNUM] or 0  -- Current step index
  local step_d = speedWalkDir[step_n+1]  -- Next direction to move
  local monk_steps = {}
  -- // This will need to wait until I can get all class info from msdp (instead of just active class/total levels)
  -- if msdp.CLASS == &quot;monk&quot; and msdp.LEVEL &gt;= 12 then
  --  for n, dir in ipairs(speedWalkDir) do
  --    if n &gt;= (step_n+1) then monk_steps[#monk_steps+1] = dir end
  --  end
  -- end
  local pos = msdp.POSITION or &quot;Sleeping&quot;
  if pos == &quot;Sleeping&quot; then send(&quot;wake&quot;) end
  if pos ~= &quot;Standing&quot; then send(&quot;stand&quot;) end
  if #monk_steps &gt; 0 then
    send(&quot;abundantstep &quot;..table.concat(monk_steps, &quot; &quot;))
    return
  end
  local door_check = msdp.ROOM.DOORS[exits[step_d]] or &quot;&quot;
  if door_check ~= &quot;&quot; then send(&quot;open &quot;..door_check..&quot; &quot;..exits[step_d]) end
  send(step_d)
end
--[[
  Speedwalk System Initialization
  ===============================
  Initiates automated pathfinding movement between rooms using calculated paths.
  Sets up speedwalk state and begins movement execution.
  Prerequisites:
  - speedWalkDir table must contain direction sequence
  - speedWalkPath table must contain room VNUM sequence
  - Both tables must be synchronized (same length, matching order)
  Process Flow:
  1. Validate path exists (speedWalkDir not empty)
  2. Activate speedwalk system state
  3. Build VNUM-to-step-index lookup table for position tracking
  4. Begin movement execution via speedwalk_check()
  Error Handling:
  - Silently returns if no path provided (graceful degradation)
  - Path validation prevents infinite loops or undefined behavior
  Integration:
  - Called by user commands or pathfinding algorithms
  - Works with existing room database and mapping system
  - Integrates with movement validation and timeout systems
]]
function doSpeedWalk()
  -- Validate that we have a path to follow
  if #speedWalkDir &lt; 1 then return end
  -- Initialize speedwalk system state
  speedwalk_active = true
  speedwalk_vnums = {}  -- VNUM to step index mapping for position tracking
  -- Build lookup table for current position determination
  -- Maps room VNUM to its position in the path sequence
  for n, vnum in ipairs(speedWalkPath) do
    speedwalk_vnums[vnum] = n
  end
  -- Begin movement execution
  speedwalk_check()
end
--[[
  Movement Event Handler
  ======================
  Processes character movement and updates the mapping system accordingly.
  Handles room creation, terrain updates, exit resolution, and map synchronization.
  Functionality:
  - Creates new rooms when entering unmapped areas
  - Updates terrain information for existing rooms
  - Resolves exit stubs by linking known rooms
  - Maintains map view centering on current position
  - Continues speedwalk execution if active
  Mapping Logic:
  1. Only processes indoor areas (skips Wilderness environments)
  2. Checks if current room exists in map database
  3. Creates room if new, updates terrain if existing
  4. Resolves any unlinked exits (stubs) to known rooms
  5. Centers map view and continues movement
  Exit Stub Resolution:
  - Gets list of unconnected exits from current room
  - For each stub, checks if target room exists in database
  - Creates bidirectional links between known rooms
  - Prevents mapping gaps and maintains connectivity
  Performance Notes:
  - Uses efficient getRoomName() check for room existence
  - Terrain updates use direct setRoomEnv() calls
  - Exit resolution processes only existing stubs
  Integration Points:
  - Called by MSDP movement events
  - Coordinates with speedwalk system
  - Updates Mudlet's mapping database
  - Maintains visual map consistency
]]
local function handle_move()
  -- Only process movement for mapped indoor areas
  if map.enabled == true and map.room_info.ENVIRONMENT ~= &quot;Wilderness&quot; then
    -- Check if we're entering a new room that needs to be created
    if not getRoomName(map.room_info.VNUM) then
      make_room()  -- Create new room with current MSDP data
    else
      -- Update existing room with current terrain information
      if terrain_types[map.room_info.TERRAIN] then
        setRoomEnv(map.room_info.VNUM, terrain_types[map.room_info.TERRAIN].id + 16)
      end
      -- Resolve any unlinked exits (stubs) to known rooms
      local stubs = getExitStubs1(map.room_info.VNUM)
      if stubs then
        for _, n in ipairs(stubs) do
          local dir = exitmap[n]  -- Convert exit number to direction name
          local id = map.room_info.EXITS[dir]  -- Get target room VNUM
          -- TODO: need to see how special exits are represented to handle those properly here
          -- Future Enhancement: Support for special exits like portals, teleporters, etc.
          -- Link to target room if it exists in the database
          if getRoomName(id) then
            setExit(map.room_info.VNUM, id, exitmap[dir])
          end
        end
      end
    end
  end
  -- Maintain map view centered on current position
  centerview(map.room_info.VNUM)
  -- Continue speedwalk execution if active
  speedwalk_check()
end
--[[
  Mapping System Alias Registration
  =================================
  Creates user-friendly command aliases for controlling the mapping system.
  Provides convenient command-line interface for map manipulation and control.
  Alias Categories:
  1. Room Manipulation - Manual position adjustments and room modification
  2. System Control - Enable/disable mapping functionality
  3. Map Management - Save/load map data and area operations
  4. Navigation - Pathfinding and movement commands
  Implementation Details:
  - Uses tempAlias() for dynamic alias creation
  - Aliases stored in map.aliases table for cleanup management
  - Event-driven architecture for loose coupling with mapping system
  - Regex patterns provide flexible command matching
  Alias Management:
  - Checks for existing aliases before creation to prevent duplicates
  - Properly cleans up old aliases using killAlias()
  - Maintains alias registry for systematic management
  Integration Points:
  - Coordinates with event system via raiseEvent()
  - Links directly to mapping functions and procedures
  - Provides user feedback through game output
]]
local function make_aliases()
  -- Initialize alias storage for management and cleanup
  map.aliases = map.aliases or {}
  -- Quick Access Aliases - Direct tempAlias calls for immediate commands
  -- Room position manipulation for map corrections
  table.insert(map.aliases,tempAlias([[^shift (\w+)$]],[[raiseEvent(&quot;shiftRoom&quot;,matches[2])]]))
  -- Mapping system control shortcuts
  table.insert(map.aliases,tempAlias([[^mc on$]],[[raiseEvent(&quot;startMapping&quot;)]]))
  table.insert(map.aliases,tempAlias([[^mc off$]],[[raiseEvent(&quot;stopMapping&quot;)]]))
  -- Comprehensive Alias Definition Table
  -- Maps descriptive names to {pattern, code} pairs for organized management
  local id
  local tbl = {
    -- Core System Control
    [&quot;Start Mapping Alias&quot;] = {[[^start mapping$]], [[map.start_mapping()]]},
    [&quot;Stop Mapping Alias&quot;] = {[[^stop mapping$]], [[map.stop_mapping()]]},
    [&quot;Shift Room Alias&quot;] = {[[^shift (\w+)$]],[[raiseEvent(&quot;shiftRoom&quot;,matches[2])]]},
    -- Map File Operations (currently disabled - TODO: implement these features)
    -- Future Enhancement: Add map import/export functionality
    --[&quot;Save Map Alias&quot;] = {[[^save map$]], [[saveMap(getMudletHomeDir() .. &quot;/map.dat&quot;)]]},
    --[&quot;Load Map Alias&quot;] = {[[^load map(?: (local))?$]], [[map.load_map(matches[2])]]},
    --[&quot;Export Map Area Alias&quot;] = {[[^export area (.*)]],[[map.export_area(matches[2])]]},
    --[&quot;Import Map Area Alias&quot;] = {[[^import area (.*)]],[[map.import_area(matches[2])]]},
    -- Room and Area Management (currently disabled - TODO: implement these features)
    -- Future Enhancement: Add room creation and editing tools
    --[&quot;Set Room Area Alias&quot;] = {[[^set area (.*)$]], [[map.set_area(matches[2])]]},
    --[&quot;Set Map Mode Alias&quot;] = {[[^map mode (\w+)$]],[[map.set_mode(matches[2])]]},
    -- Advanced Room Operations (currently disabled - TODO: implement these features)
    -- Future Enhancement: Add room merging, splitting, and advanced editing
    --[&quot;Merge Rooms Alias&quot;] = {[[^merge rooms$]], [[map.merge_rooms()]]},
    --[&quot;Add Door Alias&quot;] = {[[^add door (\w+)(?: (none|open|closed|locked)(?: (yes|no))?)?$]],[[map.set_door(matches[2],matches[3],matches[4])]]},
    --[&quot;Add Portal Alias&quot;] = {[[^add portal (.*)$]],[[map.set_portal(matches[2])]]},
    --[&quot;Set Room Exit Alias&quot;] = {[[^set exit (.+) (\d+)]],[[map.set_exit(matches[2],matches[3])]]},
    --[&quot;Clear Moves Alias&quot;] = {[[^clear moves$]], [[map.clear_moves()]]},
    -- Navigation and Pathfinding (currently disabled - TODO: implement these features)
    -- Future Enhancement: Add advanced pathfinding algorithms and custom route planning
    --[&quot;Find Me Alias&quot;] = {[[^find me$]], [[map.find_me()]]},
    --[&quot;Find Path Alias&quot;] = {[[find path ([^;]+)(?:\s*;\s*(.+))?]],[[map.find_path(matches[2],matches[3])]]},
    --[&quot;Set Recall Alias&quot;] = {[[^set recall$]],[[map.set_recall()]]},
    --[&quot;Set Character Alias&quot;] = {[[^set character (.*)$]],[[map.character = matches[2]]},
  }
  -- Register aliases with cleanup management
  for k,v in pairs(tbl) do
    -- Clean up existing alias if present to prevent conflicts
    if map.aliases[k] and exists(map.aliases[k],&quot;alias&quot;) ~= 0 then
      killAlias(map.aliases[k])
    end
    -- Create new alias and store reference for future cleanup
    id = tempAlias(v[1],v[2])
    map.aliases[k] = id
  end
end
--function map.set_door(dir,status,one_way)
--    -- adds a door on a given exit
--    if map.enabled then
--        if not map.room_info then error(&quot;Make Door: No room found.&quot;) end
--        dir = exitmap[dir] or dir
--        if not stubmap[dir] then error(&quot;Make Door: Invalid direction.&quot;) end
--        status = (status ~= &quot;&quot; and status) or &quot;closed&quot;
--        one_way = (one_way ~= &quot;&quot; and one_way) or &quot;no&quot;
--        if not table.contains({&quot;yes&quot;,&quot;no&quot;},one_way) then error(&quot;Make Door: Invalid one-way status, must be yes or no.&quot;) end
--        local exits = getRoomExits(currentRoom)
--        local target_room = exits[dir]
--        if target_room then
--            exits = getRoomExits(target_room)
--        end
--        if one_way == &quot;no&quot; and (target_room and exits[reverse_dirs[dir]] == currentRoom) then
--            add_door(target_room,reverse_dirs[dir],status)
--        end
--        add_door(currentRoom,dir,status)
--				print(&quot;Door added.&quot;)
--    end
--end
-- Map Loading Function
-- Loads map data from local file or downloads from server
-- Parameters: use_local (boolean) - if true, loads local map.dat file
function map.load_map(use_local)
  local path = getMudletHomeDir() .. &quot;/map.dat&quot;
  if use_local then
    loadMap(path)
    print(&quot;Map reloaded from local copy.&quot;)
  else
    local address = 'http://www.luminarimud.com/download/map.dat'
    downloading = true
    downloadFile(path,address)
    print(&quot;Downloading Map File.&quot;)
  end
end
--[[
  Minimap Font Size Adjustment System
  ==================================
  Dynamically calculates and applies optimal font size for minimap display based on container width.
  Ensures proper text readability and visual scaling for room names and map elements.
  Responsive Design Logic:
  - Retrieves current minimap container width
  - Applies scaling algorithm for proportional font sizing
  - Updates minimap text elements with calculated font size
  - Maintains readability across different window sizes
  Integration Points:
  - Called during minimap initialization
  - Triggered by window resize events
  - Coordinates with map display refresh systems
  - Links to overall GUI scaling management
  Performance Considerations:
  - Efficient width calculation from container
  - Minimal DOM updates for smooth resizing
  - Cached font size calculations when possible
  - Event-driven updates to prevent unnecessary recalculation
--]]
--[[
  Minimap Font Size Adjustment System
  ===================================
  Dynamically calculates and applies optimal font size for minimap display based on container dimensions.
  Ensures proper text readability and visual scaling for minimap elements.
  
  Algorithm:
  - Starts with base font size of 8
  - Incrementally increases font size until content would exceed container bounds
  - Uses minimap dimensions (width x height) for scaling calculations
  - Sets final font size to last size that fits within bounds
]]
function map.adjustMinimapFontSize()
  local w = map.minimap.get_width()
  local h = map.minimap.get_height()
  local font_size = 8
  repeat
    font_size = font_size + 1
    local width, height = calcFontSize(font_size)
    width = width * map.minimap_width
    height = height * map.minimap_height
  until (w &lt; width) or (h &lt; height)
  map.minimap_font_size = font_size - 1
  setMiniConsoleFontSize(&quot;map.minimap&quot;, map.minimap_font_size)
end
--[[
  ASCII Map Font Size Adjustment System
  ====================================
  Dynamically calculates and applies optimal font size for ASCII map display based on container width.
  Ensures proper character readability and visual scaling for text-based map representations.
  
  ASCII Map Specific Considerations:
  - Text-based room representation requires precise character spacing
  - Font size affects map grid alignment and readability
  - Monospace font requirements for proper ASCII art rendering
  - Uses asciimap dimensions for consistent scaling
]]
function map.adjustAsciimapFontSize()
  local w = map.minimap.get_width()
  local h = map.minimap.get_height()
  local font_size = 8
  repeat
    font_size = font_size + 1
    local width, height = calcFontSize(font_size)
    width = width * map.asciimap_width
    height = height * map.asciimap_height
  until (w &lt; width) or (h &lt; height)
  map.minimap_font_size = font_size - 1
  setMiniConsoleFontSize(&quot;map.minimap&quot;, map.minimap_font_size)
end
function map.calcMinimapPadding()
  local width = calcFontSize(map.minimap_font_size)
  local characters = map.minimap.get_width() / width
  return (characters - map.minimap_width) / 4
end
function map.calcAsciimapPadding()
  local width = calcFontSize(map.minimap_font_size)
  local characters = map.minimap.get_width() / width
  return (characters - (map.asciimap_width - 1)) / 4
end
-- Map Container Initialization Function
-- Creates the essential map containers needed for navigation functionality
function map.init()
  cecho(&quot;&lt;cyan&gt;[DEBUG] Initializing map containers...\n&quot;)
  
  -- Validate defaults.mapper exists
  if not defaults or not defaults.mapper then
    print(&quot;[ERROR] defaults.mapper not found - cannot initialize map containers&quot;)
    return false
  end
  
  -- setting terrain colors
  for k,v in pairs(terrain_types) do
    setCustomEnvColor(v.id + 16, v.r, v.g, v.b, 255)
  end
  
  -- making mapper window
  local info = defaults.mapper
  map.container = Geyser.Container:new({name = &quot;map.container&quot;, x = info.x, y = info.y, width = info.width, height = info.height})
  map.minimapcontainer = Geyser.Container:new({name = &quot;map.minimapcontainer&quot;, x = info.x, y = info.y, width = info.width, height = info.height})
  
  map.minimap = Geyser.MiniConsole:new({
    name=&quot;map.minimap&quot;,
    x=0, y= 0,
    width=&quot;100%&quot;, height=&quot;100%&quot;,
  }, map.minimapcontainer)
  map.minimap:setColor(&quot;black&quot;)
  map.minimapcontainer:hide()
  
  map.mapwindow = Geyser.Mapper:new({name = &quot;map.mapwindow&quot;, x = 0, y = 0, width = &quot;100%&quot;, height = &quot;100%&quot;}, map.container)
  
  map.adjustMinimapFontSize()
  make_aliases()
  map.get_default_map()
  
  cecho(&quot;&lt;green&gt;[DEBUG] Map containers initialized successfully!\n&quot;)
  return true
end

-- Legacy config function for backwards compatibility
local function config()
  return map.init()
end
function map.get_default_map()
  -- If the user has no map, download the default map from the server!
  local areas = getAreaTable()
  local path = getMudletHomeDir() .. &quot;/map.dat&quot;
  if (areas[&quot;Mosswood&quot;] == nil) then
    local address = 'http://www.luminarimud.com/download/map.dat'
    downloading = true
    downloadFile(path,address)
    print(&quot;Downloading Map File.&quot;)
  end
end
-- Mapping System Activation
-- Enables automatic room mapping and coordinates with Mudlet's mapping system
function map.start_mapping()
  map.enabled=true
  print(&quot;Mapping enabled.&quot;)
end
-- Mapping System Deactivation
-- Disables automatic room mapping
function map.stop_mapping()
  map.enabled=false
  print(&quot;Mapping disabled.&quot;)
end
-- Map Event Handler
-- Processes MSDP and game events related to mapping and room data
-- Parameters: event (string), ... (variable arguments based on event type)
function map.eventHandler(event,...)
  if event == &quot;msdp.ROOM&quot; then
    map.prev_info = map.room_info
    map.room_info = table.update({},msdp.ROOM)
    -- Check if we have moved between regular and wilderness areas
    if map.prev_info.ENVIRONMENT == &quot;Wilderness&quot; and map.room_info.ENVIRONMENT == &quot;Room&quot; then
      -- re-enable the mapper!
      map.minimapcontainer:hide()
      map.container:show()
    elseif map.prev_info.ENVIRONMENT == &quot;Room&quot; and map.room_info.ENVIRONMENT == &quot;Wilderness&quot; then
      -- disable the mapper!
      map.container:hide()
      map.minimapcontainer:show()
    end
    handle_move()
  elseif event == &quot;shiftRoom&quot; then
    local dir = exits[arg[1]] or arg[1]
    if not table.contains(exits, dir) then
      echo(&quot;Error: Invalid direction '&quot; .. dir .. &quot;'.&quot;)
    else
      shift_room(dir)
    end
  elseif event == &quot;sysDownloadDone&quot; and downloading then
    loadMap(getMudletHomeDir() .. &quot;/map.dat&quot;)
    downloading = false
    print(&quot;Map File Loaded.&quot;)
  elseif event == &quot;sysConnectionEvent&quot; then
    config()
  end
end
function map.onProtocolEnabled(_, protocol)
  if protocol == &quot;MSDP&quot; then
    print(&quot;MSDP enabled!&quot;)
    sendMSDP(&quot;REPORT&quot;, &quot;ROOM&quot;)
    sendMSDP(&quot;REPORT&quot;, &quot;POSITION&quot;)
    config()
  end
end

-- Initialize map event handlers with tracking
function map.initHandlers()
  -- Use GUI tracking system if available
  if GUI and GUI.registerHandler then
    GUI.registerHandler(&quot;msdp.ROOM&quot;, &quot;map.eventHandler&quot;, &quot;map_msdp_room&quot;)
    GUI.registerHandler(&quot;msdp.POSITION&quot;, &quot;map.eventHandler&quot;, &quot;map_msdp_position&quot;)
    GUI.registerHandler(&quot;shiftRoom&quot;, &quot;map.eventHandler&quot;, &quot;map_shift_room&quot;)
    GUI.registerHandler(&quot;sysConnectionEvent&quot;, &quot;map.eventHandler&quot;, &quot;map_sys_connection&quot;)
    GUI.registerHandler(&quot;sysProtocolEnabled&quot;, &quot;map.onProtocolEnabled&quot;, &quot;map_sys_protocol&quot;)
    GUI.registerHandler(&quot;sysDownloadDone&quot;, &quot;map.eventHandler&quot;, &quot;map_sys_download&quot;)
  else
    -- Fallback to standard handlers if tracking not available
    registerAnonymousEventHandler(&quot;msdp.ROOM&quot;,&quot;map.eventHandler&quot;)
    registerAnonymousEventHandler(&quot;msdp.POSITION&quot;,&quot;map.eventHandler&quot;)
    registerAnonymousEventHandler(&quot;shiftRoom&quot;,&quot;map.eventHandler&quot;)
    registerAnonymousEventHandler(&quot;sysConnectionEvent&quot;, &quot;map.eventHandler&quot;)
    registerAnonymousEventHandler(&quot;sysProtocolEnabled&quot;, &quot;map.onProtocolEnabled&quot;)
    registerAnonymousEventHandler(&quot;sysDownloadDone&quot;, &quot;map.eventHandler&quot;)
  end
end

-- Initialize handlers on load
map.initHandlers()</script>
					<eventHandlerList/>
				</Script>
			</ScriptGroup>
			<ScriptGroup isActive="yes" isFolder="yes">
				<name>GUI</name>
				<packageName>Template</packageName>
				<script/>
				<eventHandlerList/>
				<ScriptGroup isActive="yes" isFolder="yes">
					<name>CSSman</name>
					<packageName>CSSman</packageName>
					<script/>
					<eventHandlerList/>
					<Script isActive="yes" isFolder="no">
						<name>CSSMan</name>
						<packageName/>
						<script>-- CSSMan by Vadi. Public domain.
CSSMan = {}
CSSMan.__index = CSSMan
function CSSMan.new(stylesheet)
  local obj  = { stylesheet = {} }
  setmetatable(obj,CSSMan)
  local trim = string.trim
  assert(type(stylesheet) == &quot;string&quot;, &quot;CSSMan.new: no stylesheet provided. A possible error is that you might have used CSSMan.new, not CSSMan:new&quot;)
  for line in stylesheet:gmatch(&quot;[^\r\n]+&quot;) do
    local attribute, value = line:match(&quot;^(.-):(.-);$&quot;)
    if attribute and value then
      attribute, value = trim(attribute), trim(value)
      obj.stylesheet[attribute] = value
    end
  end
  return obj
end
function CSSMan:set(key, value)
  self.stylesheet[key] = value
end
function CSSMan:get(key)
  return self.stylesheet[key]
end
function CSSMan:getCSS(key)
  local lines, concat = {}, table.concat
  for k,v in pairs(self.stylesheet) do lines[#lines+1] = concat({k,&quot;: &quot;, v, &quot;;&quot;}) end
  return concat(lines, &quot;\n&quot;)
end
function CSSMan:gettable()
  return self.stylesheet
end
function CSSMan:settable(tbl)
  assert(type(tbl) == &quot;table&quot;, &quot;CSSMan:settable: table expected, got &quot;..type(tbl))
  self.stylesheet = tbl
end</script>
						<eventHandlerList/>
					</Script>
				</ScriptGroup>
				<ScriptGroup isActive="yes" isFolder="yes">
					<name>GUI</name>
					<packageName/>
					<script>GUI = GUI or {}
</script>
					<eventHandlerList/>
					<Script isActive="yes" isFolder="no">
						<name>Toggles</name>
						<packageName/>
						<script>--Toggle the gagging of chat from main buffer.
function GUI.gagChatToggle()
  if GUI.toggles.gagChat == false then
	  GUI.toggles.gagChat = true
		cecho(&quot;\n&lt;white&gt;Now gagging chat from main buffer.&quot;)
	elseif GUI.toggles.gagChat == true then
	  GUI.toggles.gagChat = false
		cecho(&quot;\n&lt;white&gt;No longer gagging chat from main buffer.&quot;)
	else
	  GUI.toggles = {
		       gagChat = true,
	  includeInGroup = true,
		}
		table.save(getMudletHomeDir()..&quot;/GUI.toggles.lua&quot;, GUI.toggles)
	end
end
--Toggles whether or not you are shown in the group tab.
function GUI.showSelfToggle()
  if GUI.toggles.includeInGroup == false then
	  GUI.toggles.includeInGroup = true
		GUI.updateGroup()
		cecho(&quot;\n&lt;white&gt;Now showing self in group.&quot;)
	elseif GUI.toggles.includeInGroup == true then
	  GUI.toggles.includeInGroup = false
		GUI.updateGroup()
		cecho(&quot;\n&lt;white&gt;No longer showing self in group.&quot;)
	else
	  GUI.toggles = {
		       gagChat = true,
	  includeInGroup = true,
		}
		table.save(getMudletHomeDir()..&quot;/GUI.toggles.lua&quot;, GUI.toggles)
		cecho(&quot;\n&lt;white&gt;Now showing self in group.&quot;)
	end
end
function GUI.loadToggles()
--Check for saved .lua. Create if not.
  GUI.toggles = {}
  if not io.exists(getMudletHomeDir()..&quot;/GUI.toggles.lua&quot;) then
	  GUI.toggles = {
		       gagChat = true,
	  includeInGroup = true,
		}
		table.save(getMudletHomeDir()..&quot;/GUI.toggles.lua&quot;, GUI.toggles)
	else
		table.load(getMudletHomeDir()..&quot;/GUI.toggles.lua&quot;, GUI.toggles)
  end
end
--Save Toggle Preferences
function GUI.saveToggles()
  table.save(getMudletHomeDir()..&quot;/GUI.toggles.lua&quot;, GUI.toggles)
end
-- Event handlers moved to Resource Cleanup migration function</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Create Background</name>
						<packageName/>
						<script>function GUI.init_background()
		GUI.BackgroundCSS = CSSMan.new([[
		background-color: rgb(40,40,40);
		color: white;
]])
  -- Left
  GUI.Left = Geyser.Label:new({name = &quot;GUI.Left&quot;, x = 0, y = 0, width = &quot;25%&quot;, height = &quot;100%&quot;})
  GUI.Left:setStyleSheet(GUI.BackgroundCSS:getCSS())
  --Right
	GUI.Right =
    Geyser.Label:new({name = &quot;GUI.Right&quot;, x = &quot;-25%&quot;, y = &quot;-50%&quot;, width = &quot;25%&quot;, height = &quot;50%&quot;})
  GUI.Right:setStyleSheet(GUI.BackgroundCSS:getCSS())
	--Top
	--[[
	GUI.Top = Geyser.Label:new({name = &quot;GUI.Top&quot;, x = &quot;25%&quot;, y = 0, width = &quot;50%&quot;, height = &quot;25%&quot;})
  GUI.Top:setStyleSheet(GUI.BackgroundCSS:getCSS())
	]]
	--Bottom
	GUI.Bottom = Geyser.Label:new({name = &quot;GUI.Bottom&quot;, x = &quot;25%&quot;, y = &quot;-25%&quot;, width = &quot;50%&quot;, height = &quot;25%&quot;})
  GUI.Bottom:setStyleSheet(GUI.BackgroundCSS:getCSS())
end</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Set Borders</name>
						<packageName/>
						<script>function GUI.set_borders()
  local w, h = getMainWindowSize()
  setBorderLeft(w / 4)
  setBorderTop(0)
  setBorderBottom(h / 4)
  setBorderRight(w / 4)
end</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Boxes</name>
						<packageName/>
						<script>function GUI.init_boxes()
  GUI.BoxCSS =
    CSSMan.new(
      [[
	background-color: rgba(40,40,40,200);
	background-image: url(]] ..
      getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
      [[/LuminariGUI/images/ui_texture.jpg);
	color: white;
	font-family: Tahoma, Geneva, sans-serif;
]]
    )
  --GUI.Box1 = Geyser.Label:new({
  --  name = &quot;GUI.Box1&quot;,
  --  x = 0, y = 0,
  --  width = &quot;100%&quot;,
  --  height = &quot;50%&quot;,
  --},GUI.Right)
  --GUI.Box1:setStyleSheet(GUI.BoxCSS:getCSS())
  --GUI.Box1:echo(&quot;&lt;center&gt;GUI.Box1&quot;)
	--Box2
  GUI.Box2 =
    Geyser.Label:new({name = &quot;GUI.Box2&quot;, x = 0, y = 0, width = &quot;100%&quot;, height = &quot;100%&quot;}, GUI.Bottom)
  GUI.Box2:setStyleSheet(GUI.BoxCSS:getCSS())
  GUI.Box2:echo(&quot;&lt;center&gt;GUI.Box2&quot;)
  GUI.chatContainer =
    Geyser.Label:new(
      {
        name = &quot;GUI.chatContainer&quot;,
        x = 8,
        y = &quot;15%&quot;,
        width = GUI.Box2:get_width() - 14,
				--Height subtraction is found by adding y to button height.
        height = &quot;70%&quot;,
      },
      GUI.Box2
    )
  --Box3
  GUI.Box3 =
    Geyser.Label:new(
      {name = &quot;GUI.Box3&quot;, x = &quot;0%&quot;, y = &quot;0%&quot;, width = &quot;100%&quot;, height = &quot;15%&quot;}, GUI.Right
    )
  GUI.Box3CSS = CSSMan.new(GUI.BoxCSS:getCSS())
  GUI.Box3CSS:set(&quot;border-width&quot;, &quot;0px&quot;)
  GUI.Box3:setStyleSheet(GUI.Box3CSS:getCSS())
  GUI.Box3:echo(&quot;&lt;center&gt;GUI.Box3&quot;)
  --Box4
  GUI.Box4 =
    Geyser.Label:new(
      {name = &quot;GUI.Box4&quot;, x = &quot;0%&quot;, y = &quot;0%&quot;, width = &quot;100%&quot;, height = &quot;75%&quot;}, GUI.Left
    )
  GUI.Box4CSS = CSSMan.new(GUI.BoxCSS:getCSS())
  GUI.Box4CSS:set(&quot;border-width&quot;, &quot;0px&quot;)
  GUI.Box4:setStyleSheet(GUI.Box4CSS:getCSS())
  GUI.Box4:echo(&quot;&lt;center&gt;GUI.Box4&quot;)
  --Box5
  GUI.Box5 =
    Geyser.Label:new(
      {name = &quot;GUI.Box5&quot;, x = &quot;0%&quot;, y = &quot;15%&quot;, width = &quot;100%&quot;, height = &quot;50%&quot;}, GUI.Right
    )
  GUI.Box5CSS = CSSMan.new(GUI.BoxCSS:getCSS())
  GUI.Box5CSS:set(&quot;border-width&quot;, &quot;0px&quot;)
  GUI.Box5:setStyleSheet(GUI.Box5CSS:getCSS())
  GUI.Box5:echo(&quot;&lt;center&gt;Legend When Toggled/Room Info&quot;)
 --Commenting out Box 6 echo until skill Icons are finished.
	GUI.Box6 =
    Geyser.Label:new(
      {name = &quot;GUI.Box6&quot;, x = &quot;0%&quot;, y = &quot;65%&quot;, width = &quot;100%&quot;, height = &quot;35%&quot;}, GUI.Right
    )
  GUI.Box6CSS = CSSMan.new(GUI.BoxCSS:getCSS())
  GUI.Box6CSS:set(&quot;border-width&quot;, &quot;0px&quot;)
  GUI.Box6:setStyleSheet(GUI.Box6CSS:getCSS())
  --GUI.Box6:echo(&quot;&lt;center&gt;Skill Icons&quot;)
  --Box7
  GUI.Box7 =
    Geyser.Label:new(
      {name = &quot;GUI.Box7&quot;, x = &quot;0%&quot;, y = &quot;75%&quot;, width = &quot;100%&quot;, height = &quot;25%&quot;}, GUI.Left
    )
  GUI.Box7:setStyleSheet(GUI.BoxCSS:getCSS())
end</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Gauges</name>
						<packageName/>
						<script>function GUI.init_gauges()
  GUI.Footer =
    Geyser.HBox:new(
      {name = &quot;GUI.Footer&quot;, x = 0, y = 10, width = &quot;100%&quot;, height = &quot;75%&quot;}, GUI.Bottom
    )
  GUI.Status =
    Geyser.HBox:new(
      {name = &quot;GUI.Status&quot;, x = 12, y = 12, width = &quot;100%&quot;, height = &quot;100%&quot;, margin = 11}, GUI.Box7
    )
  GUI.ActionIconsBox =
    Geyser.VBox:new(
      {
        name = &quot;GUI.ActionIconsBox&quot;,
        x = 0,
        y = 0,
        height = &quot;100%&quot;,
        width = 32,
        h_policy = Geyser.Fixed,
        v_policy = Geyser.Fixed,
      },
      GUI.Status
    )
  GUI.GaugeBar =
    Geyser.VBox:new(
      {
        name = &quot;GUI.GaugeBar&quot;,
        x = 0,
        y = 0,
        height = &quot;100%&quot;,
        width = GUI.Status.get_width() - 20 - 32,
        h_policy = Geyser.Fixed,
        v_policy = Geyser.Fixed,
      },
      GUI.Status
    )
  --GUI.LeftColumn = Geyser.VBox:new({
  --  name = &quot;GUI.LeftColumn&quot;,
  --},GUI.Footer)
  --GUI.RightColumn = Geyser.VBox:new({
  --  name = &quot;GUI.RightColumn&quot;,
  --},GUI.Footer)
  GUI.GaugeBackCSS =
    CSSMan.new(
      [[
  background-color: rgba(20,20,20,255);
  border-style: solid;
  border-color: rgb(100,100,100);
  border-width: 1px;
  border-radius: 5px;
  margin: 5px;
 width: 100%;
 font-family: BitstreamVeraSans;
 color: white;
]]
    )
  GUI.GaugeFrontCSS =
    CSSMan.new(
      [[
  background-color: rgba(40,40,40,255);
  border-style: solid;
  border-color: rgb(120,120,120);
  border-width: 1px;
  border-radius: 7px;
  margin: 5px;
 width: 100%;
 font-family: BitstreamVeraSans;
 color: white;
]]
    )
  GUI.Health =
    Geyser.Gauge:new({name = &quot;GUI.Health&quot;, height = 32, v_policy = Geyser.Fixed}, GUI.GaugeBar)
  GUI.GaugeFrontCSS:set(&quot;background-color&quot;, &quot;green&quot;)
  GUI.GaugeBackCSS:set(&quot;background-color&quot;, &quot;dark_green&quot;)
  GUI.Health.front:setStyleSheet(GUI.GaugeFrontCSS:getCSS())
  GUI.Health.back:setStyleSheet(GUI.GaugeBackCSS:getCSS())
  GUI.Health:setValue(math.random(100), 100)
  GUI.Health.front:echo(&quot;GUI.Health&quot;)
  GUI.Moves =
    Geyser.Gauge:new({name = &quot;GUI.Moves&quot;, height = 32, v_policy = Geyser.Fixed}, GUI.GaugeBar)
  GUI.GaugeFrontCSS:set(&quot;background-color&quot;, &quot;purple&quot;)
  GUI.GaugeBackCSS:set(&quot;background-color&quot;, &quot;dark_purple&quot;)
  GUI.Moves.front:setStyleSheet(GUI.GaugeFrontCSS:getCSS())
  GUI.Moves.back:setStyleSheet(GUI.GaugeBackCSS:getCSS())
  GUI.Moves:setValue(math.random(100), 100)
  GUI.Moves.front:echo(&quot;GUI.Moves&quot;)
  GUI.Experience =
    Geyser.Gauge:new({name = &quot;GUI.Experience&quot;, height = 32, v_policy = Geyser.Fixed}, GUI.GaugeBar)
  GUI.GaugeFrontCSS:set(&quot;background-color&quot;, &quot;yellow&quot;)
  GUI.GaugeBackCSS:set(&quot;background-color&quot;, &quot;dark_yellow&quot;)
  GUI.Experience.front:setStyleSheet(GUI.GaugeFrontCSS:getCSS())
  GUI.Experience.back:setStyleSheet(GUI.GaugeBackCSS:getCSS())
  GUI.Experience:setValue(math.random(100), 100)
  GUI.Experience.front:echo([[&lt;span style = &quot;color: white&quot;&gt;GUI.Experience&lt;/span&gt;]])
  GUI.Enemy =
    Geyser.Gauge:new({name = &quot;GUI.Enemy&quot;, height = 32, v_policy = Geyser.Fixed}, GUI.GaugeBar)
  GUI.GaugeFrontCSS:set(&quot;background-color&quot;, &quot;brown&quot;)
  GUI.GaugeBackCSS:set(&quot;background-color&quot;, &quot;dark_brown&quot;)
  GUI.Enemy.front:setStyleSheet(GUI.GaugeFrontCSS:getCSS())
  GUI.Enemy.back:setStyleSheet(GUI.GaugeBackCSS:getCSS())
  GUI.Enemy:setValue(math.random(100), 100)
  GUI.Enemy.front:echo([[GUI.Enemy]])
  GUI.AffectedByIconsBox =
    Geyser.HBox:new(
      {
        name = &quot;GUI.AffectedByIconsBox&quot;,
        x = 0,
        y = 0,
        height = 48,
        width = &quot;100%&quot;,
        h_policy = Geyser.Fixed,
        v_policy = Geyser.Fixed,
      },
      GUI.GaugeBar
    )
end
function GUI.init_action_icons()
  GUI.ActionIconCSS = CSSMan.new([[
	margin: 0px;
]])
  GUI.StandardActionIcon =
    Geyser.Label:new(
      {
        name = &quot;GUI.StandardActionIcon&quot;,
        width = 32,
        height = 32,
        h_policy = Geyser.Fixed,
        v_policy = Geyser.Fixed,
      },
      GUI.ActionIconsBox
    )
  GUI.ActionIconCSS:set(
    &quot;border-image&quot;,
    [[url(&quot;]] ..
    getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
    [[/LuminariGUI/images/action-standard.png&quot;);]]
  )
  GUI.StandardActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
  GUI.MoveActionIcon =
    Geyser.Label:new(
      {
        name = &quot;GUI.MoveActionIcon&quot;,
        width = 32,
        height = 32,
        h_policy = Geyser.Fixed,
        v_policy = Geyser.Fixed,
      },
      GUI.ActionIconsBox
    )
  GUI.ActionIconCSS:set(
    &quot;border-image&quot;,
    [[url(&quot;]] .. getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) .. [[/LuminariGUI/images/action-move.png&quot;);]]
  )
  GUI.MoveActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
  GUI.SwiftActionIcon =
    Geyser.Label:new(
      {
        name = &quot;GUI.SwiftActionIcon&quot;,
        width = 32,
        height = 32,
        h_policy = Geyser.Fixed,
        v_policy = Geyser.Fixed,
      },
      GUI.ActionIconsBox
    )
  GUI.ActionIconCSS:set(
    &quot;border-image&quot;,
    [[url(&quot;]] .. getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) .. [[/LuminariGUI/images/action-swift.png&quot;);]]
  )
  GUI.SwiftActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
end
--GUI.Endurance = Geyser.Gauge:new({
--  name = &quot;GUI.Endurance&quot;,
--},GUI.RightColumn)
--GUI.Endurance.back:setStyleSheet(GUI.GaugeBackCSS:getCSS())
--GUI.GaugeFrontCSS:set(&quot;background-color&quot;,&quot;yellow&quot;)
--GUI.Endurance.front:setStyleSheet(GUI.GaugeFrontCSS:getCSS())
--GUI.Endurance:setValue(math.random(100),100)
--GUI.Endurance.front:echo(&quot;GUI.Endurance&quot;)
--GUI.Endurance.front:echo([[&lt;span style = &quot;color: black&quot;&gt;GUI.Endurance&lt;/span&gt;]])
--GUI.Willpower = Geyser.Gauge:new({
--  name = &quot;GUI.Willpower&quot;,
--},GUI.RightColumn)
--GUI.Willpower.back:setStyleSheet(GUI.GaugeBackCSS:getCSS())
--GUI.GaugeFrontCSS:set(&quot;background-color&quot;,&quot;purple&quot;)
--GUI.Willpower.front:setStyleSheet(GUI.GaugeFrontCSS:getCSS())
--GUI.Willpower:setValue(math.random(100),100)
--GUI.Willpower.front:echo(&quot;GUI.Willpower&quot;)</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Cast Console</name>
						<packageName/>
--[[
  Spell Casting Console Initialization
  ===================================
  Initializes the dedicated mini-console for spell casting feedback and status tracking.
  Provides real-time visual feedback for spell casting progress, completion, and interruption.
  Console Configuration:
  - Embedded within main GUI layout for integrated display
  - Transparent background for seamless visual integration
  - Inherits parent container sizing and positioning
  - Uses Geyser.MiniConsole for advanced text formatting capabilities
  Purpose and Integration:
  - Centralized spell casting feedback display
  - Separates casting messages from main game output
  - Provides consistent formatting for all casting events
  - Coordinates with casting event handlers and timers
  Event Integration Points:
  - Connected to spell start/complete/abort/cancel event handlers
  - Integrates with casting timer systems
  - Coordinates with MSDP spell data processing
  - Links to player action feedback mechanisms
--]]
						<script>function GUI.init_castConsole()
		-- DEBUG: Log cast console initialization
		cecho(&quot;&lt;yellow&gt;[DEBUG] Initializing cast console...\n&quot;)
		cecho(&quot;&lt;yellow&gt;[DEBUG] GUI.Box2 dimensions: &quot; .. GUI.Box2:get_width() .. &quot; x &quot; .. GUI.Box2:get_height() .. &quot;\n&quot;)
		
		GUI.castConsole =
		Geyser.MiniConsole:new(
		{
				name = &quot;GUI.castConsole&quot;,
				x = 8,
				y = &quot;5%&quot;,
				width = GUI.Box2:get_width() - 15,
				-- Height subtraction is found by adding y to button height.
				height = &quot;10%&quot;,
		},
		GUI.Box2
		)
		setMiniConsoleFontSize(&quot;GUI.castConsole&quot;, getFontSize(&quot;main&quot;))
		setFont(&quot;GUI.castConsole&quot;, getFont(&quot;main&quot;))
		
		-- DEBUG: Verify cast console creation and visibility
		if GUI.castConsole then
				cecho(&quot;&lt;green&gt;[DEBUG] Cast console created successfully\n&quot;)
				cecho(&quot;&lt;yellow&gt;[DEBUG] Cast console position: x=&quot; .. GUI.castConsole.x .. &quot; y=&quot; .. GUI.castConsole.y .. &quot;\n&quot;)
				cecho(&quot;&lt;yellow&gt;[DEBUG] Cast console size: w=&quot; .. GUI.castConsole.width .. &quot; h=&quot; .. GUI.castConsole.height .. &quot;\n&quot;)
				cecho(&quot;&lt;yellow&gt;[DEBUG] Cast console visible: &quot; .. tostring(not GUI.castConsole.hidden) .. &quot;\n&quot;)
		else
				cecho(&quot;&lt;red&gt;[DEBUG] ERROR: Cast console creation failed!\n&quot;)
		end
end
-- Spell Casting Initiation Handler
-- Displays spell casting start message and sets up casting state
-- Parameters: spellName (string), spellLength (string/number)
function GUI.castConsole_startCast(spellName, spellLength)
  GUI.currentlyCasting = spellName
  GUI.castingNow = true
  clearUserWindow(&quot;GUI.castConsole&quot;)
  GUI.castConsole:cecho(&quot;\n&lt;white&gt;Spell: &lt;yellow&gt;&quot;..spellName:title()..&quot; &lt;white&gt;- &lt;cyan&gt;&quot;..spellLength)
  if GUI.castConsoleTimer then
    killTimer(GUI.castConsoleTimer)
  end
end
-- Spell Casting Completion Handler
-- Displays successful spell completion message with green status
function GUI.castConsole_completeCast()
  GUI.castConsole:cecho(&quot;\n&lt;white&gt;Spell: &lt;yellow&gt;&quot;..GUI.currentlyCasting:title()..&quot; &lt;white&gt;- &lt;green&gt;Cast&quot;)
  -- Use tracked timer if available
  if GUI.createTimer then
    GUI.castConsoleTimer = GUI.createTimer(10, [[clearUserWindow(&quot;GUI.castConsole&quot;)]], false, &quot;cast_console_clear&quot;)
  else
    GUI.castConsoleTimer = tempTimer(10, [[clearUserWindow(&quot;GUI.castConsole&quot;)]])
  end
end
-- Spell Casting Abort Handler
-- Displays spell interruption message with red status and clears casting state
function GUI.castConsole_abortedCast()
  GUI.castConsole:cecho(&quot;\n&lt;white&gt;Spell: &lt;yellow&gt;&quot;..GUI.currentlyCasting:title()..&quot; &lt;white&gt;- &lt;red&gt;Aborted&quot;)
  GUI.castingNow = false
  -- Use tracked timer if available
  if GUI.createTimer then
    GUI.castConsoleTimer = GUI.createTimer(10, [[clearUserWindow(&quot;GUI.castConsole&quot;)]], false, &quot;cast_console_clear&quot;)
  else
    GUI.castConsoleTimer = tempTimer(10, [[clearUserWindow(&quot;GUI.castConsole&quot;)]])
  end
end
-- Spell Casting Cancel Handler
-- Displays spell cancellation message with red status and clears casting state
function GUI.castConsole_canceledCast()
  GUI.castConsole:cecho(&quot;\n&lt;white&gt;Spell: &lt;yellow&gt;&quot;..GUI.currentlyCasting:title()..&quot; &lt;white&gt;- &lt;red&gt;Canceled&quot;)
  GUI.castingNow = false
  -- Use tracked timer if available
  if GUI.createTimer then
    GUI.castConsoleTimer = GUI.createTimer(10, [[clearUserWindow(&quot;GUI.castConsole&quot;)]], false, &quot;cast_console_clear&quot;)
  else
    GUI.castConsoleTimer = tempTimer(10, [[clearUserWindow(&quot;GUI.castConsole&quot;)]])
  end
end
</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Header Icons</name>
						<packageName/>
						<script>function GUI.init_header_icons()
  GUI.Header =
    Geyser.HBox:new({name = &quot;GUI.Header&quot;, x = 0, y = 0, width = &quot;100%&quot;, height = &quot;100%&quot;}, GUI.Top)
  GUI.IconCSS =
    CSSMan.new(
      [[
  background-color: rgba(0,0,0,100);
  border-style: solid;
  border-width: 1px;
  border-color: rgb(160,160,160);
  border-radius: 5px;
  margin: 5px;
  qproperty-wordWrap: true;
]]
    )
  --[[
for i=1,12 do
  GUI[&quot;Icon&quot;..i] = Geyser.Label:new({
    name = &quot;GUI.Icon&quot;..i,
  },GUI.Header)
  GUI[&quot;Icon&quot;..i]:setStyleSheet(GUI.IconCSS:getCSS())
  GUI[&quot;Icon&quot;..i]:echo(&quot;&lt;center&gt;GUI. Icon&quot;..i)
end
]]
end</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>TabbedInfoWindow</name>
						<packageName/>
						<script>GUI.tabbedInfoWindow =
  GUI.tabbedInfoWindow or
  {
    tabs = {&quot;Player&quot;, &quot;Affects&quot;, &quot;Group&quot;},
    color1 = &quot;rgba(40,40,40,220)&quot;,
    color2 = &quot;rgba(30,30,30,220)&quot;,
    width = &quot;100%&quot;,
    height = &quot;100%&quot;,
    current = &quot;Player&quot;,
  }
--[[
  Dynamic UI Frame Generator
  ==========================
  Creates decorative border frames around GUI containers using 9-slice image technique.
  Automatically adapts to container dimensions and provides professional visual styling.
  Parameters:
    fl (Geyser.Container) - Target container to frame
  Frame Architecture:
  - 9-slice border system using corner, edge, and repeating segments
  - Corner pieces: Fixed 133x133 or 9x133 pixel dimensions
  - Edge pieces: Dynamically sized based on container dimensions
  - Repeating middle sections for scalable borders
  Image Asset Structure:
  - Corner frames: 4 fixed-size corner pieces (upper/lower + left/right)
  - Edge repeaters: 4 repeating edge pieces for sides and top/bottom
  - All images stored in LuminariGUI/images/frame/ directory
  Coordinate System:
  - Uses Geyser positioning with negative values for right/bottom alignment
  - Dynamic sizing calculations: container_size - 266 for middle sections
  - Maintains consistent 9px and 133px frame element dimensions
  Performance Considerations:
  - Creates 12 separate Geyser.Label elements per frame
  - CSS border-image styling for image rendering
  - Uses stretch and repeat modes for proper scaling
  Usage Context:
  - Applied to major GUI containers (Box2, Box3, Box4, Box5, Box7)
  - Provides visual hierarchy and professional appearance
  - Separates content areas with clear boundaries
]]
function createFrame(fl)
  -- Initialize frame table to hold all border elements
  ft = {}
  -- Left Border Elements
  -- ====================
  -- Upper left corner - fixed size corner piece
  ft.upper_left = Geyser.Label:new({x = 0, y = 0, height = 133, width = 9}, fl)
  ft.upper_left:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
    [[/LuminariGUI/images/frame/frame_upperleft_9x133.png) 0 0 0 0 stretch stretch;
  ]]
  )
  -- Middle left edge - repeating section that scales with container height
  ft.middle_left =
    Geyser.Label:new({x = 0, y = 133, height = fl:get_height() - 266, width = 9}, fl)
  ft.middle_left:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
    [[/LuminariGUI/images/frame/straight_repeater_left_9x133.png) 0 0 0 0 stretch repeat;
  ]]
  )
  -- Lower left corner - fixed size corner piece at bottom
  ft.lower_left = Geyser.Label:new({x = 0, y = -133, height = 133, width = 9}, fl)
  ft.lower_left:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
    [[/LuminariGUI/images/frame/frame_lowerleft_9x133.png) 0 0 0 0 stretch stretch;
  ]]
  )
  -- Right Border Elements
  -- =====================
  -- Upper right corner - fixed size corner piece
  ft.upper_right = Geyser.Label:new({x = -9, y = 0, height = 133, width = 9}, fl)
  ft.upper_right:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
    [[/LuminariGUI/images/frame/frame_upperright_9x133.png) 0 0 0 0 stretch stretch;
  ]]
  )
  -- Middle right edge - repeating section that scales with container height
  ft.middle_right =
    Geyser.Label:new({x = -9, y = 133, height = fl:get_height() - 266, width = 9}, fl)
  ft.middle_right:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
    [[/LuminariGUI/images/frame/straight_repeater_right_9x133.png) 0 0 0 0 stretch repeat;
  ]]
  )
  -- Lower right corner - fixed size corner piece at bottom
  ft.lower_right = Geyser.Label:new({x = -9, y = -133, height = 133, width = 9}, fl)
  ft.lower_right:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
    [[/LuminariGUI/images/frame/frame_lowerright_9x133.png) 0 0 0 0 stretch stretch;
  ]]
  )
  -- Top Border Elements
  -- ===================
  -- Left top corner - fixed size corner piece
  ft.left_top = Geyser.Label:new({x = 0, y = 0, height = 9, width = 133}, fl)
  ft.left_top:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
    [[/LuminariGUI/images/frame/frame_lefttop_133x9.png) 0 0 0 0 stretch stretch;
  ]]
  )
  -- Middle top edge - repeating section that scales with container width
  ft.middle_top =
    Geyser.Label:new({x = 133, y = 0, height = 9, width = fl:get_width() - 266}, fl)
  ft.middle_top:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
    [[/LuminariGUI/images/frame/straight_repeater_top_133x9.png) 0 0 0 0 stretch repeat;
  ]]
  )
  -- Right top corner - fixed size corner piece
  ft.right_top = Geyser.Label:new({x = -133, y = 0, height = 9, width = 133}, fl)
  ft.right_top:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
    [[/LuminariGUI/images/frame/frame_righttop_133x9.png) 0 0 0 0 stretch stretch;
  ]]
  )
  -- Bottom Border Elements
  -- ======================
  -- Left bottom corner - fixed size corner piece
  ft.left_bottom = Geyser.Label:new({x = 0, y = -9, height = 9, width = 133}, fl)
  ft.left_bottom:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
    [[/LuminariGUI/images/frame/frame_leftbottom_133x9.png) 0 0 0 0 stretch stretch;
  ]]
  )
  -- Middle bottom edge - repeating section that scales with container width
  ft.middle_bottom =
    Geyser.Label:new({x = 133, y = -9, height = 9, width = fl:get_width() - 266}, fl)
  ft.middle_bottom:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
    [[/LuminariGUI/images/frame/straight_repeater_bottom_133x9.png) 0 0 0 0 stretch repeat;
  ]]
  )
  -- Right bottom corner - fixed size corner piece
  ft.right_bottom = Geyser.Label:new({x = -133, y = -9, height = 9, width = 133}, fl)
  ft.right_bottom:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
    [[/LuminariGUI/images/frame/frame_rightbottom_133x9.png) 0 0 0 0 stretch stretch;
  ]]
  )
end
function GUI.tabbedInfoWindow.click(tab)
  GUI.tabbedInfoWindow[GUI.tabbedInfoWindow.current]:hide()
  GUI.tabbedInfoWindow[GUI.tabbedInfoWindow.current .. &quot;tab&quot;]:echo(
    GUI.tabbedInfoWindow.current, &quot;white&quot;, &quot;c&quot;
  )
  GUI.tabbedInfoWindow.current = tab
  GUI.tabbedInfoWindow[GUI.tabbedInfoWindow.current]:show()
  GUI.tabbedInfoWindow[tab .. &quot;tab&quot;]:echo(tab, &quot;yellow&quot;, &quot;c&quot;)
end
function GUI.tabbedInfoWindow.init()
  GUI.tabbedInfoWindowTabCSS =
    CSSMan.new(
      [[font-family: Tahoma, Geneva, sans-serif;
			border-image: url(]] ..
      getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
      [[/LuminariGUI/images/buttons/button.png) 0 0 0 0 stretch stretch;]]
    )
  GUI.tabbedInfoWindow.container =
    Geyser.Container:new(
      {
        name = &quot;GUI.tabbedInfoWindow.back&quot;,
        x = 9,
        y = 9,
        width = GUI.Box4:get_width() - 18,
        height = GUI.Box4:get_height() - 18,
      },
      GUI.Box4
    )
  GUI.tabbedInfoWindow.header =
    Geyser.HBox:new(
      {name = &quot;GUI.tabbedInfoWindow.header&quot;, x = 0, y = 0, width = &quot;100%&quot;, height = &quot;5%&quot;},
      GUI.tabbedInfoWindow.container
    )
  GUI.tabbedInfoWindow.footer =
    Geyser.Label:new(
      {name = &quot;GUI.tabbedInfoWindow.footer&quot;, x = 0, y = &quot;5%&quot;, width = &quot;100%&quot;, height = &quot;95%&quot;},
      GUI.tabbedInfoWindow.container
    )
  GUI.tabbedInfoWindow.footer:setStyleSheet([[font-family: Tahoma, Geneva, sans-serif;
]])
  GUI.tabbedInfoWindow.center =
    Geyser.Label:new(
      {name = &quot;GUI.tabbedInfoWindow.center&quot;, x = 0, y = 0, width = &quot;100%&quot;, height = &quot;100%&quot;},
      GUI.tabbedInfoWindow.footer
    )
  GUI.tabbedInfoWindow.center:setStyleSheet(
    [[
  background-color: ]] ..
    GUI.tabbedInfoWindow.color2 ..
    [[
	font-family: Tahoma, Geneva, sans-serif;
	]]
  )
  for k, v in pairs(GUI.tabbedInfoWindow.tabs) do
    GUI.tabbedInfoWindow[v .. &quot;tab&quot;] =
      Geyser.Label:new({name = &quot;GUI.tabbedInfoWindow.&quot; .. v .. &quot;tab&quot;}, GUI.tabbedInfoWindow.header)
    GUI.tabbedInfoWindow[v .. &quot;tab&quot;]:setStyleSheet(GUI.tabbedInfoWindowTabCSS:getCSS())
    GUI.tabbedInfoWindow[v .. &quot;tab&quot;]:echo(&quot;&lt;center&gt;&quot; .. v)
    GUI.tabbedInfoWindow[v .. &quot;tab&quot;]:setClickCallback(&quot;GUI.tabbedInfoWindow.click&quot;, v)
    GUI.tabbedInfoWindow[v] =
      Geyser.Label:new(
        {name = &quot;GUI.tabbedInfoWindow.&quot; .. v, x = 0, y = 0, width = &quot;100%&quot;, height = &quot;100%&quot;},
        GUI.tabbedInfoWindow.footer
      )
    GUI.tabbedInfoWindow[v]:setStyleSheet(
      [[
    background-color: ]] ..
      GUI.tabbedInfoWindow.color1 ..
      [[;
	  font-family: Tahoma, Geneva, sans-serif;
	]]
    )
    GUI.tabbedInfoWindow[v .. &quot;center&quot;] =
      Geyser.Label:new(
        {
          name = &quot;GUI.tabbedInfoWindow.&quot; .. v .. &quot;center&quot;,
          x = 0,
          y = 0,
          width = &quot;100%&quot;,
          height = &quot;100%&quot;,
        },
        GUI.tabbedInfoWindow[v]
      )
    GUI.tabbedInfoWindow[v .. &quot;center&quot;]:setStyleSheet(
      [[
    background-color: ]] ..
      GUI.tabbedInfoWindow.color2 ..
      [[;
		border-image: url(]] ..
      getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
      [[/LuminariGUI/images/ui_texture.jpg) 0 0 0 0 stretch stretch;
		font-family: Tahoma, Geneva, sans-serif;
  ]]
    )
    GUI.tabbedInfoWindow[v]:hide()
    GUI.tabbedInfoWindow.current = v
  end
  -- Init to player tab
  GUI.tabbedInfoWindow.click(&quot;Player&quot;)
end</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Affects</name>
						<packageName/>
						<script>-- Affects
-- -------
-- There are several types of affects in Luminari - Affected By (Status), Spell-Like Affects, Resistances and Damage Reduction
-- 'Affected By' also includes 'Modes' which change the character's behavior in some way, for example 'Flurry-of-Blows' or
-- 'Rapid-Shot'.  Modes are displayed in the box with the health bars, as this is important information even if you are not
-- Interested in the other types of affects.
--
-- Spell-like affects can create 'Affected By' status flags.
--
-- All other affects are displayed in the 'Affects' tab in the upper left.
--
-- Container: GUI.tabbedInfoWindow[&quot;Affectscenter&quot;]
--
-- Initialize tables
GUI.AffectIcons = GUI.AffectIcons or {}
GUI.SLAffects = GUI.SLAffects or {}
GUI.SLAffects.Labels = GUI.SLAffects.Labels or {}
GUI.Affects = GUI.Affects or {}
GUI.Affects.Rows = GUI.Affects.Rows or {}
GUI.Affects.Modes = GUI.Affects.Modes or {}
function GUI.Affects.init()
  -- Initialize all the labels and such
  GUI.Affects = {}
  GUI.Affects.Labels = {}
  GUI.Affects.Modes = {}
  GUI.Affects.Modes.Labels = {}
  GUI.Affects.Rows = {}
  -- CSS
  GUI.Affects.IconCSS = CSSMan.new([[
	  margin: 0px;
  ]])
  GUI.Affects.Modes.IconCSS = CSSMan.new([[
	  margin: 0px;
  ]])
  -- Calculate how many labels will fit into the container
  GUI.Affects.icon_width = 48
  GUI.Affects.icon_height = 48
  GUI.Affects.Modes.icon_width = 48
  GUI.Affects.Modes.icon_height = 48
  GUI.Affects.num_icons_row =
    (GUI.tabbedInfoWindow[&quot;Affectscenter&quot;]:get_width()) / GUI.Affects.icon_width
  GUI.Affects.num_slots_row = GUI.Affects.num_icons_row
  GUI.Affects.num_rows = 3 -- This is so we have room below for the other affect types.
    --(GUI.tabbedInfoWindow[&quot;Affectscenter&quot;]:get_height()) / GUI.Affects.icon_height
	-- Create the VBox
  GUI.Affects.container =
    Geyser.VBox:new(
      {
        name = &quot;GUI.Affects.container&quot;,
        x = 0,
        y = 0,
        width = &quot;100%&quot;,
        height = GUI.Affects.num_rows * GUI.Affects.icon_height,
        h_policy = Geyser.Fixed,
        v_policy = Geyser.Fixed,
      },
      GUI.tabbedInfoWindow[&quot;Affectscenter&quot;]
    )
	-- Create the Affect Labels
  for i = 1, GUI.Affects.num_rows do
    --GUI.Affects.icon_width * GUI.Affects.num_icons_row,
    GUI.Affects.Rows[i] = {}
    GUI.Affects.Rows[i].EffectSlots = {}
    GUI.Affects.Rows[i].container =
      Geyser.HBox:new(
        {
          name = &quot;GUI.Affects.Row&quot; .. i,
          x = 0,
          y = 0,
          height = GUI.Affects.icon_height,
          width = &quot;100%&quot;,
          h_policy = Geyser.Fixed,
          v_policy = Geyser.Fixed,
        },
        GUI.Affects.container
      )
    for j = 1, GUI.Affects.num_icons_row do
      GUI.Affects.Rows[i].EffectSlots[j] = {}
      
      -- Create container for this effect slot (VBox for icon + label)
      GUI.Affects.Rows[i].EffectSlots[j].container =
        Geyser.VBox:new(
          {
            name = &quot;GUI.Affects.EffectSlot&quot; .. tostring(i) .. '_' .. tostring(j),
            width = GUI.Affects.icon_width,
            height = GUI.Affects.icon_height,
            h_policy = Geyser.Fixed,
            v_policy = Geyser.Fixed,
          },
          GUI.Affects.Rows[i].container
        )
      
      -- Create icon label for the effect image
      GUI.Affects.Rows[i].EffectSlots[j].icon =
        Geyser.Label:new(
          {
            name = &quot;GUI.Affects.EffectIcon&quot; .. tostring(i) .. '_' .. tostring(j),
            width = &quot;100%&quot;,
            height = GUI.Affects.icon_height,
            h_policy = Geyser.Fixed,
            v_policy = Geyser.Fixed,
          },
          GUI.Affects.Rows[i].EffectSlots[j].container
        )
      
      -- Create text label for the effect name
      GUI.Affects.Rows[i].EffectSlots[j].label =
        Geyser.Label:new(
          {
            name = &quot;GUI.Affects.EffectLabel&quot; .. tostring(i) .. '_' .. tostring(j),
            width = &quot;100%&quot;,
            height = 16,
            h_policy = Geyser.Fixed,
            v_policy = Geyser.Fixed,
          },
          GUI.Affects.Rows[i].EffectSlots[j].container
        )
      
      -- Set initial styling and hide the container
      GUI.Affects.Rows[i].EffectSlots[j].icon:setStyleSheet(GUI.Affects.IconCSS:getCSS())
      GUI.Affects.Rows[i].EffectSlots[j].icon:setColor(0, 0, 0, 0)
      GUI.Affects.Rows[i].EffectSlots[j].label:setAlignment(&quot;AlignCenter&quot;)
      GUI.Affects.Rows[i].EffectSlots[j].container:hide()
    end
    GUI.Affects.current_row = 1
    GUI.Affects.current_column = 1
  end
  -- Initialize Modes - These will display along with the health bars.
  GUI.Affects.Modes.num_icons_row =
    GUI.AffectedByIconsBox:get_width() / GUI.Affects.Modes.icon_width
  GUI.Affects.Modes.ModeList =
    {
      [&quot;Mode-RapidShot&quot;] = true,
      [&quot;Flurry-of-Blows&quot;] = true,
      [&quot;Sneaking&quot;] = true,
      [&quot;Hiding&quot;] = true,
      [&quot;Mode-PowerAttack&quot;] = true,
      [&quot;Mode-Expertise&quot;] = true,
      [&quot;Mode-Total-Defense&quot;] = true,
      [&quot;Spot-Mode&quot;] = true,
      [&quot;Listen-Mode&quot;] = true,
      [&quot;Mode-Spellbattle&quot;] = true,
      [&quot;Counterspell&quot;] = true,
      [&quot;Defensive-Casting&quot;] = true,
      [&quot;Charging&quot;] = true,
      [&quot;WildShape&quot;] = true,
    }
  -- Create the Mode Labels
  for i = 1, GUI.Affects.Modes.num_icons_row do
    GUI.Affects.Modes.Labels[i] =
      Geyser.Label:new(
        {
          name = &quot;GUI.AffectIcon&quot; .. tostring(i),
          width = GUI.Affects.Modes.icon_width,
          height = GUI.Affects.Modes.icon_height,
          h_policy = Geyser.Fixed,
          v_policy = Geyser.Fixed,
        },
        GUI.AffectedByIconsBox
      )
    GUI.Affects.Modes.Labels[i]:setStyleSheet(GUI.Affects.Modes.IconCSS:getCSS())
  end
	GUI.tabbedInfoWindow[&quot;Affects&quot;]:hide()
	-- Initialize the area for the Spell Like Affects - GUI.Affects.SLAffects
	GUI.SLAffects.row_height = 20
	-- Calculate how many rows we can have:
	GUI.SLAffects.num_rows = (GUI.tabbedInfoWindow[&quot;Affectscenter&quot;]:get_height() - (GUI.Affects.icon_height*GUI.Affects.num_rows)) / GUI.SLAffects.row_height
	-- Set up the VBox
	GUI.SLAffects.container =
	Geyser.VBox:new(
      {
        name = &quot;GUI.SLAffects.container&quot;,
        x = 0,
        y = GUI.Affects.icon_height*GUI.Affects.num_rows,
        width = &quot;100%&quot;,
        height = GUI.tabbedInfoWindow[&quot;Affectscenter&quot;].get_height() - (GUI.Affects.icon_height*GUI.Affects.num_rows),
        v_policy = Geyser.Fixed,
      },
      GUI.tabbedInfoWindow[&quot;Affectscenter&quot;]
  )
	for i = 1, GUI.SLAffects.num_rows do
		GUI.SLAffects.Labels[i] =
			Geyser.Label:new(
				{
					name = &quot;GUI.SLAffect&quot; .. tostring(i),
					width = &quot;100%&quot;,
					height = GUI.SLAffects.row_height,
					h_policy = Geyser.Fixed,
				}, GUI.SLAffects.container
			)
		GUI.SLAffects.Labels[i]:setStyleSheet([[
        background-color: rgba(0,0,0,0%);
      ]])
    GUI.SLAffects.Labels[i]:hide()
	end
end
function GUI.updateSLAffects()
	if msdp.AFFECTS and msdp.AFFECTS.SPELL_LIKE_AFFECTS and #msdp.AFFECTS.SPELL_LIKE_AFFECTS &gt; 0 then
		-- We have spell like affects to process!
		for i = 1, GUI.SLAffects.num_rows do
			GUI.SLAffects.Labels[i]:echo(&quot;&quot;)
			GUI.SLAffects.Labels[i]:hide()
			if i &lt;= #msdp.AFFECTS.SPELL_LIKE_AFFECTS then
			  if (msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].LOCATION == &quot;Damage-Reduction&quot;) then
					affect_string = string.format(&quot;&lt;pre&gt;[ %s ] %s %s (%s)&lt;/pre&gt;&quot;, msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].DURATION, msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].NAME,
					msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].LOCATION, msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].TYPE)
				else
				  affect_string = string.format(&quot;&lt;pre&gt;[ %s ] %s %s to %s (%s)&lt;/pre&gt;&quot;, msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].DURATION, msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].NAME,
					msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].MODIFIER, msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].LOCATION, msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].TYPE)
--[[
  Character Affects Icon Display Manager
  =====================================
  Processes and displays visual icons for all character affects/buffs received via MSDP.
  Manages two distinct display categories with different priorities and layouts.
  Affect Categories:
  1. Mode Affects - Combat/behavioral modes (displayed in health bar area for immediate visibility)
  2. Status Affects - General buffs/debuffs (displayed in dedicated affects tab grid)
  Mode Affects (High Priority Display):
  - Combat modes like &quot;Flurry-of-Blows&quot;, &quot;Rapid-Shot&quot;, &quot;Sneaking&quot;, &quot;Hiding&quot;
  - Behavioral changes like &quot;Mode-PowerAttack&quot;, &quot;Mode-Expertise&quot;, &quot;Defensive-Casting&quot;
  - Displayed horizontally alongside health gauges for instant visibility
  - Limited slots available (GUI.Affects.Modes.num_icons_row)
  Status Affects (Tab Display):
  - All other affects that don't qualify as modes
  - Displayed in grid layout within Affects tab (3 rows configurable)
  - Uses row/column positioning system for organized display
  Data Flow:
  1. Validates MSDP affects data existence and non-empty state
  2. Resets all icon displays (hide previous icons)
  3. Iterates through all affects and categorizes each
  4. Populates mode icons first (priority display)
  5. Fills remaining affects in tab grid layout
  Icon Management:
  - Dynamic CSS styling with border-image for visual representation
  - Icons loaded from LuminariGUI/images/affected_by/[AFFECT_NAME].png
  - Automatic layout management with overflow handling
  - Position tracking via current_icon, current_row, current_column counters
  Performance Considerations:
  - Batch hide operations for efficient UI cleanup
  - Single-pass categorization and display algorithm
  - CSS update only when affect changes (not every refresh)
  Integration Points:
  - Called by msdp.AFFECTS event handler
  - Coordinates with GUI.Affects initialization system
  - Depends on pre-configured GUI layout containers
]]
				end
				GUI.SLAffects.Labels[i]:echo(affect_string)
				GUI.SLAffects.Labels[i]:show()
			end
		end
	end
end
function GUI.updateAffectIcons()
  if msdp.AFFECTS and msdp.AFFECTS.AFFECTED_BY and #msdp.AFFECTS.AFFECTED_BY &gt; 0 then
    -- We have a status to process!  Do MODES first.
    affected_by = msdp.AFFECTS.AFFECTED_BY
    for k, _ in ipairs(GUI.Affects.Modes.Labels) do
      GUI.Affects.Modes.Labels[k]:hide()
    end
    -- Hide all effect slots
    for k, _ in ipairs(GUI.Affects.Rows) do
      for l, _ in ipairs(GUI.Affects.Rows[k].EffectSlots) do
        GUI.Affects.Rows[k].EffectSlots[l].container:hide()
      end
    end
    GUI.Affects.Modes.current_icon = 1
    GUI.Affects.current_row = 1
    GUI.Affects.current_column = 1
    for i = 1, #affected_by do
      -- Is this a Mode?
      if
        (
          (GUI.Affects.Modes.current_icon &lt;= GUI.Affects.Modes.num_icons_row) and
          (GUI.Affects.Modes.ModeList[affected_by[i].NAME] == true)
        )
      then
        -- Create the Mode Icon!
        GUI.Affects.Modes.IconCSS:set(
          &quot;border-image&quot;,
          [[url(&quot;]] ..
          getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
          [[/LuminariGUI/images/affected_by/]] ..
          affected_by[i].NAME ..
          [[.png&quot;);]]
        )
        GUI.Affects.Modes.Labels[GUI.Affects.Modes.current_icon]:setStyleSheet(
          GUI.Affects.Modes.IconCSS:getCSS()
        )
        GUI.Affects.Modes.Labels[GUI.Affects.Modes.current_icon]:show()
        GUI.Affects.Modes.current_icon = GUI.Affects.Modes.current_icon + 1
      else
        -- This is just an Affected By status flag - show in tab with icon + label
        if
          (
            (GUI.Affects.current_column &lt;= GUI.Affects.num_slots_row) and
            (GUI.Affects.current_row &lt;= GUI.Affects.num_rows)
          )
        then
          -- Get the current effect slot
          local currentSlot = GUI.Affects.Rows[GUI.Affects.current_row].EffectSlots[GUI.Affects.current_column]
          
          -- Set the icon
          GUI.Affects.IconCSS:set(
            &quot;border-image&quot;,
            [[url(&quot;]] ..
            getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
            [[/LuminariGUI/images/affected_by/]] ..
            affected_by[i].NAME ..
            [[.png&quot;);]]
          )
          currentSlot.icon:setStyleSheet(GUI.Affects.IconCSS:getCSS())
          
          -- Set the label text - clean up the effect name for display
          local effectName = affected_by[i].NAME
          -- Replace hyphens and underscores with spaces and title case
          effectName = effectName:gsub(&quot;[-_]&quot;, &quot; &quot;)
          effectName = effectName:gsub(&quot;(%w)(%w*)&quot;, function (a,b) return string.upper(a)..string.lower(b) end)
          currentSlot.label:echo(&quot;&lt;center&gt;&quot; .. effectName .. &quot;&lt;/center&gt;&quot;)
          
          -- Show the complete slot
          currentSlot.container:show()
          
          GUI.Affects.current_column = GUI.Affects.current_column + 1
          if GUI.Affects.current_column &gt; GUI.Affects.num_slots_row then
            GUI.Affects.current_column = 1
            GUI.Affects.current_row = GUI.Affects.current_row + 1
          end
        end
      end
    end
  end
end</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Group</name>
						<packageName/>
						<script>function GUI.init_group()
  GUI.GroupConsole =
    Geyser.MiniConsole:new(
      {name = &quot;GUI.GroupConsole&quot;, x = 0, y = 0, width = &quot;100%&quot;, height = &quot;100%&quot;},
      GUI.tabbedInfoWindow[&quot;Groupcenter&quot;]
    )
  GUI.GroupConsole:setColor({r=0, g=0, b=0, a=255})
end
--[[
  Group Data Processing and Display Manager
  =========================================
  Processes and formats group member data received via MSDP for display in the Group tab.
  Manages player inclusion toggle and prepares data structure for console output formatting.
  Core Functionality:
  - Processes raw MSDP.GROUP data into formatted display structure
  - Handles player inclusion/exclusion toggle (GUI.toggles.includeInGroup)
  - Filters and reorganizes group member information
  - Prepares data for console formatting and display
  Player Toggle Logic:
  - When includeInGroup = false: Excludes player character from group display
  - When includeInGroup = true: Includes all group members including player
  - Uses CHARACTER_NAME comparison for player identification
  Data Structure Processing:
  Input (msdp.GROUP): Raw group data array from MSDP protocol
  Output (GUI.group_data): Filtered and formatted array for display
  Group Member Properties:
  - IS_LEADER: Boolean indicating group leadership status
  - NAME: Character name string
  - HEALTH/HEALTH_MAX: Current and maximum health values
  - MOVEMENT/MOVEMENT_MAX: Current and maximum movement points
  - LEVEL: Character level integer
  - CLASS_STRING: Character class description
  Array Management:
  - Uses sparse array indexing with j counter
  - Dynamically finds next available slot in GUI.group_data
  - Maintains consistent data structure for display formatting
  Integration Points:
  - Called by MSDP group data event handlers
  - Coordinates with GUI.toggles configuration system
  - Feeds processed data to group console display functions
  - Depends on msdp.CHARACTER_NAME for player identification
  Performance Considerations:
  - Single-pass filtering algorithm
  - Minimal data transformation overhead
  - Efficient sparse array management
  - Direct property copying for speed
]]
function GUI.updateGroup()
  GUI.group_data = {}
  -- Remove the player from the group data if toggled off.
  local j = 1
  for i = 1, #msdp.GROUP do
    if GUI.toggles.includeInGroup == false and msdp.GROUP[i].NAME ~= msdp.CHARACTER_NAME then
      while GUI.group_data[j] ~= nil do
        j = j + 1
      end
      GUI.group_data[j] = {}
      GUI.group_data[j].IS_LEADER = msdp.GROUP[i].IS_LEADER
      GUI.group_data[j].NAME = msdp.GROUP[i].NAME
      GUI.group_data[j].HEALTH = msdp.GROUP[i].HEALTH
      GUI.group_data[j].HEALTH_MAX = msdp.GROUP[i].HEALTH_MAX
      GUI.group_data[j].MOVEMENT = msdp.GROUP[i].MOVEMENT
      GUI.group_data[j].MOVEMENT_MAX = msdp.GROUP[i].MOVEMENT_MAX
      GUI.group_data[j].LEVEL = msdp.GROUP[i].LEVEL
      GUI.group_data[j].CLASS_STRING = msdp.GROUP[i].CLASS_STRING
  -- Add the player from the group data if toggled on.
		elseif GUI.toggles.includeInGroup == true then
      while GUI.group_data[j] ~= nil do
        j = j + 1
      end
      GUI.group_data[j] = {}
      GUI.group_data[j].IS_LEADER = msdp.GROUP[i].IS_LEADER
      GUI.group_data[j].NAME = msdp.GROUP[i].NAME
      GUI.group_data[j].HEALTH = msdp.GROUP[i].HEALTH
      GUI.group_data[j].HEALTH_MAX = msdp.GROUP[i].HEALTH_MAX
      GUI.group_data[j].MOVEMENT = msdp.GROUP[i].MOVEMENT
      GUI.group_data[j].MOVEMENT_MAX = msdp.GROUP[i].MOVEMENT_MAX
      GUI.group_data[j].LEVEL = msdp.GROUP[i].LEVEL
      GUI.group_data[j].CLASS_STRING = msdp.GROUP[i].CLASS_STRING
    end -- if/elseif
  end -- for
	clearUserWindow(&quot;GUI.GroupConsole&quot;)
  num_grouped = #GUI.group_data
  for i = 1, num_grouped do
    -- Build the group member data and send it.
    --	+----------------------------------+
    --  | [level] Name                     |
    --  | hp: 90%  |=================    | |
    --  | Crown (leader), Shield (Tank)    |
    --  +----------------------------------+
    -- Leader status display removed - was unused
    -- if GUI.group_data[i].IS_LEADER == &quot;1&quot; then
    --   status_msg = &quot;&lt;green&gt;LEADER&quot;
    -- else
    --   status_msg = &quot;&quot;
    -- end
    local pct_health
    local health = tonumber(GUI.group_data[i].HEALTH)
    local max_health = tonumber(GUI.group_data[i].HEALTH_MAX)
    if health &gt; 0 then
      pct_health = (health / max_health) * 100
    else
      pct_health = 0
    end
    if pct_health &gt; 60 then
      hp_color = &quot;&lt;lime&gt;&quot;
    elseif pct_health &gt; 15 then
        hp_color = &quot;&lt;yellow&gt;&quot;
      else
        hp_color = &quot;&lt;red&gt;&quot;
      end
			local formatted_level = &quot;[&quot; .. GUI.group_data[i].LEVEL .. &quot;]&quot;
			local formatted_name = GUI.group_data[i].NAME
			formatted_level = string.cut(formatted_level..&quot;   &quot;, 4)
			formatted_name = string.cut(formatted_name..&quot;                     &quot;, 23)
			local member = string.format(&quot;&lt;cyan&gt;[%2s] &lt;white&gt;%-23s: &lt;cyan&gt;[%s%3s&lt;cyan&gt;/%-3s]H [%3s/%-3s]V\n&quot;
																	 , GUI.group_data[i].LEVEL
																	 , GUI.group_data[i].NAME
																	 , hp_color
																	 , GUI.group_data[i].HEALTH
																	 , GUI.group_data[i].HEALTH_MAX
																	 , GUI.group_data[i].MOVEMENT
																	 , GUI.group_data[i].MOVEMENT_MAX)
      GUI.GroupConsole:cecho(member)
      --GUI.GroupConsole:cecho(status_msg .. &quot;\n&quot;)
    end
  end
</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Player</name>
						<packageName/>
						<script>function GUI.init_player()
  -- Set stylesheet with : qproperty-alignment: 'AlignLeft | AlignTop';
	GUI.tabbedInfoWindow[&quot;Playercenter&quot;]:setStyleSheet([[
		qproperty-alignment: 'AlignLeft | AlignTop';
	]]);
end
function GUI.updatePlayer()
    GUI.tabbedInfoWindow[&quot;Playercenter&quot;]:echo(
		[[&lt;p style=&quot;font-size:16px;font-style:normal;font-family: 'Bitstream Vera Sans Mono'&quot;&gt;]] ..
    (msdp.CHARACTER_NAME or &quot;Unknown&quot;) ..	&quot; (&quot;..(msdp.POSITION or &quot;Unknown&quot;)..&quot;)&quot;..
    [[&lt;br/&gt;Level ]] ..
    (msdp.LEVEL or &quot;Unknown&quot;) ..
    [[ ]] ..
    (msdp.CLASS or &quot;Unknown&quot;) ..
    [[&lt;br/&gt;]] ..
    (msdp.RACE or &quot;Unknown&quot;) ..
    [[&lt;br/&gt;]] ..
    [[&lt;br/&gt;Str: ]] ..
    (msdp.STR or &quot;Unknown&quot;) ..
    [[&lt;br/&gt;Dex: ]] ..
    (msdp.DEX or &quot;Unknown&quot;) ..
    [[&lt;br/&gt;Con: ]] ..
    (msdp.CON or &quot;Unknown&quot;) ..
    [[&lt;br/&gt;Int: ]] ..
    (msdp.INT or &quot;Unknown&quot;) ..
    [[&lt;br/&gt;Wis: ]] ..
    (msdp.WIS or &quot;Unknown&quot;) ..
    [[&lt;br/&gt;Cha: ]] ..
    (msdp.CHA or &quot;Unknown&quot;) ..
    [[&lt;br/&gt;]] ..
    [[&lt;br/&gt;AC: ]] ..
    (msdp.AC or &quot;Unknown&quot;) ..
    [[&lt;br/&gt;]] ..
    [[&lt;br/&gt;Gold: ]] ..
    (msdp.MONEY or &quot;Unknown&quot;) ..
    [[&lt;br/&gt;]] ..
		[[&lt;/font&gt;&lt;/p&gt;]]
  )
end</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Buttons</name>
						<packageName/>
						<script>GUI.buttonWindow =
  GUI.buttonWindow or
  {
    button = {"Legend", "Mudlet", "ASCII"},
    color1 = "rgba(40,40,40,255)",
    color2 = "rgba(60,60,60,255)",
    width = "100%",
    height = "100%",
    roomOrLegend = "Room",
    mudletOrAscii = "Mudlet",
    --# of chars wide text needs to be
    legendWidth = 50,
    --# of lines needed to display text
    legendHeight = 11,
  }

-- Simple map mode management using direct container manipulation
-- Navigation uses direct show/hide calls on map containers for reliability
--Determine font size for Legend
--[[
  Legend Button Font Size Adjustment
  =================================
  Dynamically calculates and applies optimal font size for the Legend button based on container width.
  Ensures proper text readability and visual scaling across different window sizes.
  Calculation Logic:
  - Retrieves current Legend button width
  - Applies scaling algorithm to determine appropriate font size
  - Updates button styling with calculated font size
  Responsive Design:
  - Automatically adapts to window resize events
  - Maintains consistent text-to-button size ratios
  - Ensures readability across different display resolutions
  - Called during button window initialization
  - Triggered by window resize events
--]]
function GUI.buttonWindow.adjustLegendFont()
  local w = GUI.buttonWindow.Legend.get_width()
  local h = GUI.buttonWindow.Legend.get_height()
  local font_size = 8
  repeat
    font_size = font_size + 1
    local width, height = calcFontSize(font_size)
    width = width * GUI.buttonWindow.legendWidth
    height = height * GUI.buttonWindow.legendHeight
  until (w &lt; width) or (h &lt; height)
	GUI.buttonWindow.Legend_font_size = font_size - 1
  setMiniConsoleFontSize(&quot;GUI.buttonWindow.Legend&quot;, GUI.buttonWindow.Legend_font_size)
end
-- Button callback for Legend - toggles between room info and legend display
function GUI.buttonWindow.legendClick()
  --Toggle For Room Info/Legend
	if GUI.buttonWindow.roomOrLegend == &quot;Room&quot; then
		GUI.buttonWindow.roomInfo:hide()
    GUI.buttonWindow.Legend:show()
		GUI.buttonWindow.roomOrLegend = &quot;Legend&quot;
		GUI.updateRoom()
		GUI.buttonWindow.Legendbutton:echo(&quot;Legend&quot;, &quot;yellow&quot;, &quot;c&quot;)
	elseif GUI.buttonWindow.roomOrLegend == &quot;Legend&quot; then
		GUI.updateLegend()
		GUI.buttonWindow.roomInfo:show()
    GUI.buttonWindow.Legend:hide()
		GUI.buttonWindow.roomOrLegend = &quot;Room&quot;
		GUI.buttonWindow.Legendbutton:echo(&quot;Legend&quot;, &quot;white&quot;, &quot;c&quot;)
	end
end
-- Button callback for Mudlet map - switches to Mudlet's native map display
function GUI.buttonWindow.mudletClick()
  map.minimapcontainer:hide()
  map.container:show()
  GUI.buttonWindow.mudletOrAscii = "Mudlet"
  GUI.buttonWindow.Mudletbutton:echo("Mudlet", "yellow", "c")
  GUI.buttonWindow.ASCIIbutton:echo("ASCII", "white", "c")
end

-- Button callback for ASCII map - switches to ASCII text-based map display  
function GUI.buttonWindow.asciiClick()
  map.minimapcontainer:show()
  map.container:hide()
  GUI.buttonWindow.mudletOrAscii = "ASCII"
  GUI.buttonWindow.ASCIIbutton:echo("ASCII", "yellow", "c")
  GUI.buttonWindow.Mudletbutton:echo("Mudlet", "white", "c")
end
-- Button Window Initialization - sets up styling and layout for map control buttons
function GUI.buttonWindow.init()
  -- Validate required GUI boxes exist before initialization
  if not GUI.Box3 then
    print(&quot;[ERROR] GUI.Box3 not found - navigation buttons cannot be initialized&quot;)
    return false
  end
  if not GUI.Box5 then
    print(&quot;[ERROR] GUI.Box5 not found - navigation display area cannot be initialized&quot;)
    return false
  end
  
  cecho(&quot;&lt;cyan&gt;[DEBUG] Initializing buttonWindow...\n&quot;)
  
  GUI.buttonWindowCSS =
    CSSMan.new(
      [[font-family: Tahoma, Geneva, sans-serif;
  	background-color: rgba(80,80,80,255);
  	border-image: url(]] ..
      getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
      [[/LuminariGUI/images/buttons/button.png) 0 0 0 0 stretch stretch;
  	color: white;
  	border: 1px solid rgb(160,160,160);]]
    )
--Entire Box3 Container for Buttons
  GUI.buttonWindow.container =
    Geyser.Container:new(
      {
        name = &quot;GUI.buttonWindow.container&quot;,
        x = 9,
        y = 9,
        width = GUI.Box3:get_width() - 18,
        height = GUI.Box3:get_height() - 18,
      },
      GUI.Box3
    )
--Room Info
  GUI.buttonWindow.roomInfo =
	Geyser.Label:new(
		{
		  name = &quot;GUI.buttonWindow.roomInfo&quot;,
			x = 9,
			y = 9,
			width = GUI.Box5:get_width()- 18,
			height = GUI.Box5:get_height() - 18,
			},
			GUI.Box5
		)
--Map Legend
  GUI.buttonWindow.Legend =
	Geyser.MiniConsole:new(
		{
		  name = &quot;GUI.buttonWindow.Legend&quot;,
			x = 9,
			y = 9,
			width = GUI.Box5:get_width()- 18,
			height = GUI.Box5:get_height() - 18,
			},
			GUI.Box5
		)
		GUI.buttonWindow.adjustLegendFont()
--Button Container
  GUI.buttonWindow.buttonContainer =
    Geyser.HBox:new(
      {name = &quot;GUI.buttonWindow.buttonContainer&quot;, x = 0, y = 0, width = &quot;100%&quot;, height = &quot;100%&quot;},
      GUI.buttonWindow.container
    )
--Draw Buttons
  for k, v in pairs(GUI.buttonWindow.button) do
    GUI.buttonWindow[v .. &quot;button&quot;] =
      Geyser.Label:new({name = &quot;GUI.buttonWindow.&quot; .. v .. &quot;button&quot;}, GUI.buttonWindow.buttonContainer)
    GUI.buttonWindow[v .. &quot;button&quot;]:setStyleSheet(GUI.buttonWindowCSS:getCSS())
    GUI.buttonWindow[v .. &quot;button&quot;]:echo(&quot;&lt;center&gt;&quot; .. v)
    cecho(&quot;&lt;cyan&gt;[DEBUG] Created button: &quot; .. v .. &quot;\n&quot;)
  end
  
  -- Validate that buttons were created successfully
  if not GUI.buttonWindow.Legendbutton then
    print(&quot;[ERROR] Legend button failed to initialize&quot;)
    return false
  end
  if not GUI.buttonWindow.Mudletbutton then
    print(&quot;[ERROR] Mudlet button failed to initialize&quot;)
    return false
  end
  if not GUI.buttonWindow.ASCIIbutton then
    print(&quot;[ERROR] ASCII button failed to initialize&quot;)
    return false
  end
  
--Legend Call Back
  GUI.buttonWindow.Legendbutton:setClickCallback(&quot;GUI.buttonWindow.legendClick&quot;)
--Mudlet/ASCII Callback
  GUI.buttonWindow.Mudletbutton:setClickCallback(&quot;GUI.buttonWindow.mudletClick&quot;)
  GUI.buttonWindow.ASCIIbutton:setClickCallback(&quot;GUI.buttonWindow.asciiClick&quot;)
--Show Room Info on Startup
	GUI.buttonWindow.roomInfo:show()
  GUI.buttonWindow.Legend:hide()
--Populating Legend
	GUI.updateLegend()
-- Initialize button states - set default to Mudlet mode
GUI.buttonWindow.mudletOrAscii = GUI.buttonWindow.mudletOrAscii or &quot;Mudlet&quot;
if GUI.buttonWindow.mudletOrAscii == &quot;Mudlet&quot; then
  GUI.buttonWindow.Mudletbutton:echo(&quot;Mudlet&quot;, &quot;yellow&quot;, &quot;c&quot;)
  GUI.buttonWindow.ASCIIbutton:echo(&quot;ASCII&quot;, &quot;white&quot;, &quot;c&quot;)
else
  GUI.buttonWindow.ASCIIbutton:echo(&quot;ASCII&quot;, &quot;yellow&quot;, &quot;c&quot;)
  GUI.buttonWindow.Mudletbutton:echo(&quot;Mudlet&quot;, &quot;white&quot;, &quot;c&quot;)
end

cecho(&quot;&lt;green&gt;[DEBUG] ButtonWindow initialization complete!\n&quot;)
return true
end
</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Room Info/Legend</name>
						<packageName/>
						<script>
function GUI.updateRoom()
  if msdp.ROOM.ENVIRONMENT == &quot;Wilderness&quot; then
    GUI.buttonWindow.roomInfo:echo(
		  [[&lt;p style=&quot;font-size:]]..tostring(getFontSize())..[[px;font-style:normal;font-family: ']]..getFont()..[['&quot;&gt;]] ..
      [[&lt;span style=&quot;color: white;&quot;&gt;]] ..
      (msdp.ROOM.ENVIRONMENT or &quot;Unknown&quot;) ..
      [[&lt;br/&gt;&amp;nbsp;Coords: &lt;span style=&quot;color: cyan;&quot;&gt;]] ..
      (&quot;(&quot;..msdp.ROOM.COORDS.X..&quot;, &quot;..msdp.ROOM.COORDS.Y..&quot;)&quot; or &quot;Unknown&quot;) ..
      [[&lt;/span&gt;&lt;br/&gt;Terrain: &lt;span style=&quot;color: yellow;&quot;&gt;]] ..
      (msdp.ROOM.TERRAIN or &quot;Unknown&quot;) ..
      [[&lt;/span&gt;&lt;/span&gt;&lt;br/&gt;]] ..
		  [[&lt;/font&gt;&lt;/p&gt;]]
    )
	else
    GUI.buttonWindow.roomInfo:echo(
		  [[&lt;p style=&quot;font-size:]]..tostring(getFontSize())..[[px;font-style:normal;font-family: ']]..getFont()..[['&quot;&gt;]] ..
      [[&lt;span style=&quot;color: white;&quot;&gt;&lt;br/&gt;&amp;nbsp;&amp;nbsp;&amp;nbsp;Room: &lt;span style=&quot;color: cyan;&quot;&gt;]] ..
      (msdp.ROOM.NAME or &quot;Unknown&quot;) ..
      [[&lt;/span&gt;&lt;br/&gt;&amp;nbsp;Room #: &lt;span style=&quot;color: lime;&quot;&gt;]] ..
      (msdp.ROOM.VNUM or &quot;Unknown&quot;) ..
      [[&lt;/span&gt;&lt;br/&gt;Terrain: &lt;span style=&quot;color: yellow;&quot;&gt;]] ..
      (msdp.ROOM.TERRAIN or &quot;Unknown&quot;) ..
      [[&lt;/span&gt;&lt;br/&gt;&amp;nbsp;&amp;nbsp;&amp;nbsp;Area: &lt;span style=&quot;color: cyan;&quot;&gt;]] ..
      (msdp.ROOM.AREA or &quot;Unknown&quot;) ..
	 -- [[&lt;br/&gt;&amp;nbsp;Levels: ]] ..
   -- (msdp.ROOM.WHATTT or &quot;Unknown&quot;) ..
      [[&lt;/span&gt;&lt;/span&gt;&lt;br/&gt;]] ..
		  [[&lt;/font&gt;&lt;/p&gt;]]
    )
  end
end
-- Terrain Symbol Mapping for Dynamic Legend Generation
-- Maps terrain types to their ASCII symbols for legend display
local terrain_symbols = {
  -- Indoor/Structural terrains
  ["Inside"] = ".",
  ["City"] = "C", 
  ["Cave"] = "C",
  -- Natural outdoor terrains
  ["Field"] = ",",
  ["Forest"] = "Y",
  ["Hills"] = "^",
  ["Low Mountains"] = "m",
  ["High Mountains"] = "M",
  ["Desert"] = ".",
  ["Jungle"] = "Y",
  ["Tundra"] = ".",
  ["Taiga"] = "Y",
  ["Beach"] = ".",
  ["Marshland"] = ",",
  -- Water terrains
  ["Water (Swim)"] = "~",
  ["Water (No Swim)"] = "=",
  ["Ocean"] = "o",
  ["Underwater"] = "U",
  -- Aerial and special movement
  ["In Flight"] = "^",
  -- Special/Magical locations  
  ["Zone Entrance"] = "X",
  ["Outer Planes"] = ".",
  ["Lava"] = ".",
  -- Road system
  ["Road North-South"] = "|",
  ["Road East-West"] = "-", 
  ["Road Intersection"] = "+",
  ["Dirt Road North-South"] = "|",
  ["Dirt Road East-West"] = "-",
  ["Dirt Road Intersection"] = "+",
  -- Underdark terrain variants
  ["Underdark - Wild"] = "Y",
  ["Underdark - City"] = "C",
  ["Underdark - Inside"] = ".",
  ["Underdark - Water (Swim)"] = "~",
  ["Underdark - Water (No Swim)"] = "=",
  ["Underdark - In Flight"] = "^",
}

--[[
  Dynamic Legend Generation System
  ===============================
  Generates map legend dynamically based on actual terrain_types configuration.
  Displays terrain colors and symbols that match the actual mapper implementation.
  
  Benefits:
  - Always accurate representation of actual terrain colors
  - Automatically includes new terrains when added to terrain_types
  - Consistent color coding between legend and map display
  - Organized display with logical grouping of terrain types
]]
function GUI.updateLegend()
  clearUserWindow("GUI.buttonWindow.Legend")
  
  -- Header information
  GUI.buttonWindow.Legend:decho("\n &lt;white&gt;Map Legend - Terrain Types")
  GUI.buttonWindow.Legend:decho("\n &lt;gray&gt;=" .. string.rep("=", 35) .. "=")
  
  -- Special navigation symbols (not tied to terrain)
  GUI.buttonWindow.Legend:decho("\n &lt;red&gt; + &lt;white&gt; Up        &lt;red&gt; - &lt;white&gt; Down      &lt;white&gt;[&lt;cyan&gt;&amp;&lt;white&gt;] &lt;white&gt;You")
  GUI.buttonWindow.Legend:decho("\n")
  
  -- Create sorted list of terrain types for organized display
  local terrain_list = {}
  for terrain_name, terrain_data in pairs(terrain_types) do
    table.insert(terrain_list, {name = terrain_name, data = terrain_data})
  end
  
  -- Sort by terrain ID for consistent display order
  table.sort(terrain_list, function (a, b) return a.data.id &lt; b.data.id end)
  
  -- Generate legend entries from actual terrain configuration
  local items_per_line = 2
  local current_line = ""
  local item_count = 0
  
  for _, terrain_info in ipairs(terrain_list) do
    local terrain_name = terrain_info.name
    local terrain_data = terrain_info.data
    local symbol = terrain_symbols[terrain_name] or "?"
    
    -- Format terrain entry with actual color values
    local color_code = string.format("&lt;%d,%d,%d&gt;", terrain_data.r, terrain_data.g, terrain_data.b)
    local entry = string.format("[%s%s&lt;white&gt;] %s", color_code, symbol, terrain_name)
    
    -- Pad entry to consistent width for alignment
    entry = string.format("%-22s", entry)
    
    current_line = current_line .. entry
    item_count = item_count + 1
    
    -- Display line when we have enough items or reached end
    if item_count >= items_per_line or terrain_info == terrain_list[#terrain_list] then
      GUI.buttonWindow.Legend:decho("\n " .. current_line)
      current_line = ""
      item_count = 0
    end
  end
  
  -- Footer with additional information
  GUI.buttonWindow.Legend:decho("\n")
  GUI.buttonWindow.Legend:decho("\n &lt;gray&gt;Total terrain types: " .. #terrain_list)
  GUI.buttonWindow.Legend:decho("\n &lt;gray&gt;Legend generated dynamically")
end
</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>DrawFrames</name>
						<packageName/>
						<script>function GUI.draw_frames()
  createFrame(GUI.Box2)
	createFrame(GUI.Box3)
  createFrame(GUI.Box4)
  createFrame(GUI.Box5)
  -- Box6 frame enabled for navigation consistency
	createFrame(GUI.Box6)
  createFrame(GUI.Box7)
end</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>MSDP</name>
						<packageName/>
						<script>function GUI.onProtocolEnabled(_, protocol)
    if protocol == &quot;MSDP&quot; then
      print(&quot;[GUI] Initializing MSDP variables&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;CHARACTER_NAME&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;POSITION&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;RACE&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;CLASS&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;ALIGNMENT&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;LEVEL&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;STR&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;DEX&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;CON&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;INT&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;WIS&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;CHA&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;AC&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;MONEY&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;HEALTH&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;HEALTH_MAX&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;OPPONENT_HEALTH&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;OPPONENT_HEALTH_MAX&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;OPPONENT_NAME&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;MOVEMENT&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;MOVEMENT_MAX&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;EXPERIENCE&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;EXPERIENCE_TNL&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;EXPERIENCE_MAX&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;ACTIONS&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;GROUP&quot;)
      --sendMSDP(&quot;REPORT&quot;, &quot;INVENTORY&quot;)
      sendMSDP(&quot;REPORT&quot;, &quot;AFFECTS&quot;)
    end
  end
--[[
  Player Health Gauge Update System
  ================================
  Updates the player health gauge display based on real-time MSDP data.
  Provides visual feedback for current health status with dynamic gauge and percentage display.
  Core Functionality:
  - Processes MSDP health data for visual representation
  - Updates gauge fill percentage based on current vs maximum health
  - Displays numerical health values and percentages
  - Applies color coding for health status indication
  Health Status Color Coding:
  - Green: High health (typically 75%+)
  - Yellow: Medium health (typically 25-75%)
  - Red: Critical health (typically &lt;25%)
  - Dynamic color transitions for smooth visual feedback
  Data Sources:
  - msdp.HEALTH: Current health points
  - msdp.HEALTH_MAX: Maximum health points
  - Health percentage calculated dynamically
  Visual Components:
  - Progress bar/gauge for visual health representation
  - Numerical display of current/maximum health
  - Percentage display for quick reference
  - Color-coded background for status awareness
  Integration Points:
  - Called by MSDP health data event handlers
  - Coordinates with combat system feedback
  - Links to healing and damage processing systems
  - Integrates with overall player status display
  Performance Considerations:
  - Efficient percentage calculations
  - Minimal GUI updates for smooth performance
  - Optimized color transitions
  - Event-driven updates only when health changes
]]
  function GUI.updateHealthGauge()
    -- This function handles the following events:
    --   msdp.HEALTH
    --   msdp.HEALTH_MAX
    -- Calculate a percentage
    local pct_health
    local health = tonumber(msdp.HEALTH) or 0
    local max_health = tonumber(msdp.HEALTH_MAX) or health
    -- Overfilled check removed - variable was unused
    -- Future enhancement: add visual effect for overfilled bars
    if health &gt; max_health then
      health = max_health
    end
    if health &gt; 0 then
      pct_health = (health / max_health) * 100
    else
      pct_health = 0
    end
    GUI.Health.front:echo(&quot;H: &quot; .. msdp.HEALTH .. &quot;/&quot; .. (msdp.HEALTH_MAX or msdp.HEALTH))
    GUI.Health.back:echo(&quot;H: &quot; .. msdp.HEALTH .. &quot;/&quot; .. (msdp.HEALTH_MAX or msdp.HEALTH))
    if pct_health &gt; 100 then
      GUI.Health:setValue(100, 100)
    else
      GUI.Health:setValue(pct_health, 100)
    end
  end
  function GUI.updateMovesGauge()
    -- This function handles the following events:
    --   msdp.MOVEMENT
    --   msdp.MOVEMENT_MAX
    -- Calculate a percentage
    local pct_moves
    local moves = tonumber(msdp.MOVEMENT) or 0
    local max_moves = tonumber(msdp.MOVEMENT_MAX) or moves
    if moves &gt; 0 then
      pct_moves = (moves / max_moves) * 100
    else
      pct_moves = 0
    end
    GUI.Moves.front:echo(&quot;Mv: &quot; .. msdp.MOVEMENT .. &quot;/&quot; .. (msdp.MOVEMENT_MAX or msdp.MOVEMENT))
    GUI.Moves.back:echo(&quot;Mv: &quot; .. msdp.MOVEMENT .. &quot;/&quot; .. (msdp.MOVEMENT_MAX or msdp.MOVEMENT))
    GUI.Moves:setValue(pct_moves, 100)
  end
  function GUI.updateExperienceGauge()
    -- This function handles the following events:
    --   msdp.EXPERIENCE
    --   msdp.EXPERIENCE_MAX
    -- Calculate a percentage
    local pct_xp
    local xp = tonumber(msdp.EXPERIENCE_MAX)
    local xp_tnl = tonumber(msdp.EXPERIENCE_TNL) or 0
    if xp &gt; 0 then
      pct_xp = (xp / (xp + xp_tnl)) * 100
    else
      pct_xp = 0
    end
    GUI.Experience.front:echo(
    [[&lt;span style = &quot;color: white&quot;&gt;XP: ]] .. xp .. &quot;/&quot; .. (xp + xp_tnl) .. [[&lt;/span&gt;]]
    )
    GUI.Experience.back:echo([[&lt;span style = &quot;color: white&quot;&gt;XP: ]] .. xp .. &quot;/&quot; .. (xp + xp_tnl) .. [[&lt;/span&gt;]])
    if pct_xp &gt; 100 then
      GUI.Experience:setValue(100, 100)
    else
      GUI.Experience:setValue(pct_xp, 100)
    end
  end
  function GUI.updateEnemyGauge()
    -- This function handles the following events:
    --   msdp.OPPONENT_HEALTH
    --   msdp.OPPONENT_HEALTH_MAX
    --   msdp.OPPONENT_NAME
    -- Show/Hide the gauge.
    if msdp.OPPONENT_NAME == &quot;&quot; then
      GUI.Enemy:hide()
    else
      GUI.Enemy:show()
    end
    -- Calculate a percentage
    local pct_health
    local health = tonumber(msdp.OPPONENT_HEALTH) or 0
    local max_health = tonumber(msdp.OPPONENT_HEALTH_MAX) or health
    if health &gt; 0 then
      pct_health = (health / max_health) * 100
    else
      pct_health = 0
    end
    local opponent_name = msdp.OPPONENT_NAME or &quot;Unknown&quot;
    GUI.Enemy.front:echo(opponent_name .. &quot;: &quot; .. health .. &quot;/&quot; .. max_health)
    GUI.Enemy.back:echo(opponent_name .. &quot;: &quot; .. health .. &quot;/&quot; .. max_health)
    if pct_health &gt; 100 then
      GUI.Enemy:setValue(100, 100)
    else
      GUI.Enemy:setValue(pct_health, 100)
    end
  end
  function GUI.updateActionIcons()
    if (msdp.ACTIONS.STANDARD_ACTION == &quot;1&quot;) then
      GUI.ActionIconCSS:set(
      &quot;border-image&quot;,
      [[url(&quot;]] ..
      getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
      [[/LuminariGUI/images/action-standard.png&quot;);]]
      )
      GUI.StandardActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
    end
    if (msdp.ACTIONS.MOVE_ACTION == &quot;1&quot;) then
      GUI.ActionIconCSS:set(
      &quot;border-image&quot;,
      [[url(&quot;]] .. getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) .. [[/LuminariGUI/images/action-move.png&quot;);]]
      )
      GUI.MoveActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
    end
    if (msdp.ACTIONS.SWIFT_ACTION == &quot;1&quot;) then
      GUI.ActionIconCSS:set(
      &quot;border-image&quot;,
      [[url(&quot;]] .. getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) .. [[/LuminariGUI/images/action-swift.png&quot;);]]
      )
      GUI.SwiftActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
    end
    if (msdp.ACTIONS.STANDARD_ACTION == &quot;0&quot;) then
      GUI.ActionIconCSS:set(
      &quot;border-image&quot;,
      [[url(&quot;]] ..
      getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
      [[/LuminariGUI/images/action-standard-50.png&quot;);]]
      )
      GUI.StandardActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
    end
    if (msdp.ACTIONS.MOVE_ACTION == &quot;0&quot;) then
      GUI.ActionIconCSS:set(
      &quot;border-image&quot;,
      [[url(&quot;]] ..
      getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
      [[/LuminariGUI/images/action-move-50.png&quot;);]]
      )
      GUI.MoveActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
    end
    if (msdp.ACTIONS.SWIFT_ACTION == &quot;0&quot;) then
      GUI.ActionIconCSS:set(
      &quot;border-image&quot;,
      [[url(&quot;]] ..
      getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
      [[/LuminariGUI/images/action-swift-50.png&quot;);]]
      )
      GUI.SwiftActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
    end
end
</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Config</name>
						<packageName/>
						<script>-- This needs to be outside the config, since YATCO needs to have the boxes set.
GUI.image_location = getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) .. &quot;/LuminariGUI/images/&quot;
GUI.init_background()
GUI.set_borders()
GUI.init_boxes()
function GUI.init()
  cecho(&quot;&lt;green&gt;[DEBUG] Starting GUI initialization...\n&quot;)
  
  GUI.AffectIcons = GUI.AffectIcons or {}
  GUI.Affects = GUI.Affects or {}
  GUI.Affects.Rows = GUI.Affects.Rows or {}
  GUI.Affects.Modes = GUI.Affects.Modes or {}
  --
  cecho(&quot;&lt;yellow&gt;[DEBUG] Initializing GUI components...\n&quot;)
  GUI.init_gauges()
  GUI.init_action_icons()
  --GUI.init_header_icons()
  GUI.tabbedInfoWindow.init()
  GUI.init_group()
  if GUI.Affects.init then GUI.Affects.init() end
  GUI.draw_frames()
  
  -- Initialize map containers before button window
  cecho(&quot;&lt;cyan&gt;[DEBUG] About to initialize map containers...\n&quot;)
  if map.init then 
    map.init()
  else
    print(&quot;[WARNING] map.init function not found&quot;)
  end
  
  GUI.buttonWindow.init()
  
  cecho(&quot;&lt;cyan&gt;[DEBUG] About to initialize cast console...\n&quot;)
  GUI.init_castConsole()
  cecho(&quot;&lt;cyan&gt;[DEBUG] Cast console initialization complete\n&quot;)
  
  GUI.styleScrollbar()
  
  cecho(&quot;&lt;green&gt;[DEBUG] GUI initialization complete!\n&quot;)
  
  -- Use the resource cleanup migration function for all event handlers
  GUI.migrateEventHandlers()
end

-- These handlers stay untracked since they're needed to bootstrap the system
registerAnonymousEventHandler(&quot;sysLoadEvent&quot;, &quot;GUI.init&quot;)
registerAnonymousEventHandler(&quot;sysInstall&quot;, &quot;GUI.init&quot;)</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Resource Cleanup</name>
						<packageName/>
						<script>--[[
  Resource Cleanup Manager
  =======================
  Centralized management system for all event handlers and timers in LuminariGUI.
  Prevents memory leaks by tracking and cleaning up all resources on exit/uninstall.
  
  Architecture:
  - All event handlers are stored with unique IDs in GUI.handlers table
  - All timers are stored with unique IDs in GUI.timers table
  - Cleanup functions automatically called on sysUninstall and sysExitEvent
  - Helper functions provided for safe resource creation
  
  Usage:
  - Use GUI.registerHandler() instead of registerAnonymousEventHandler()
  - Use GUI.createTimer() instead of tempTimer()
  - Resources are automatically cleaned up on exit
--]]

-- Initialize resource tracking tables
GUI.handlers = GUI.handlers or {}
GUI.timers = GUI.timers or {}

-- Helper function to safely register event handlers with tracking
function GUI.registerHandler(event, func, name)
  name = name or (event .. "_" .. tostring(func))
  
  -- Kill existing handler if it exists
  if GUI.handlers[name] then
    killAnonymousEventHandler(GUI.handlers[name])
  end
  
  -- Register new handler and store ID
  local handlerId = registerAnonymousEventHandler(event, func)
  GUI.handlers[name] = handlerId
  
  return handlerId
end

-- Helper function to safely create timers with tracking
function GUI.createTimer(time, func, recurring, name)
  name = name or ("timer_" .. tostring(os.time()) .. "_" .. tostring(math.random()))
  
  -- Kill existing timer if it exists
  if GUI.timers[name] then
    killTimer(GUI.timers[name])
  end
  
  -- Create appropriate timer type
  local timerId
  if recurring then
    timerId = tempTimer(time, func, true)
  else
    timerId = tempTimer(time, func)
  end
  
  GUI.timers[name] = timerId
  return timerId
end

-- Cleanup all event handlers
function GUI.cleanupHandlers()
  local count = 0
  for name, handlerId in pairs(GUI.handlers) do
    if handlerId then
      killAnonymousEventHandler(handlerId)
      count = count + 1
    end
  end
  GUI.handlers = {}
  cecho(string.format("&lt;green&gt;[Resource Cleanup] Cleaned up %d event handlers\n", count))
  return count
end

-- Cleanup all timers
function GUI.cleanupTimers()
  local count = 0
  for name, timerId in pairs(GUI.timers) do
    if timerId then
      killTimer(timerId)
      count = count + 1
    end
  end
  GUI.timers = {}
  cecho(string.format("&lt;green&gt;[Resource Cleanup] Cleaned up %d timers\n", count))
  return count
end

-- Master cleanup function
function GUI.cleanupResources()
  cecho("&lt;yellow&gt;[Resource Cleanup] Starting resource cleanup...\n")
  
  -- Clean up specific known resources first
  
  -- 1. Cast console timer
  if GUI.castConsoleTimer then
    killTimer(GUI.castConsoleTimer)
    GUI.castConsoleTimer = nil
  end
  
  -- 2. YATCO blink timer
  if demonnic and demonnic.chat and demonnic.chat.blinkID then
    killTimer(demonnic.chat.blinkID)
    demonnic.chat.blinkID = nil
    demonnic.chat.blinkTimerOn = false
  end
  
  -- 3. Speedwalk timer (from mapper)
  if speedwalk_timer then
    killTimer(speedwalk_timer)
    speedwalk_timer = nil
  end
  
  -- 4. Any YATCO initialization timer
  if GUI.yatcoInitTimer then
    killTimer(GUI.yatcoInitTimer)
    GUI.yatcoInitTimer = nil
  end
  
  -- Clean up all tracked resources
  local handlerCount = GUI.cleanupHandlers()
  local timerCount = GUI.cleanupTimers()
  
  -- Save toggles before exit
  if GUI.saveToggles then
    GUI.saveToggles()
  end
  
  cecho(string.format("&lt;green&gt;[Resource Cleanup] Cleanup complete - removed %d handlers and %d timers\n", 
    handlerCount, timerCount))
end

-- Register cleanup handlers - these use the standard function to avoid recursion
registerAnonymousEventHandler("sysUninstall", "GUI.cleanupResources")
registerAnonymousEventHandler("sysExitEvent", "GUI.cleanupResources")

-- Migration function to update existing handlers to use tracking
function GUI.migrateEventHandlers()
  -- This function will be called from the modified Config script
  -- to re-register all handlers with tracking
  
  cecho("&lt;cyan&gt;[Resource Cleanup] Migrating event handlers to tracked system...\n")
  
  -- GUI event handlers
  GUI.registerHandler("msdp.GROUP", "GUI.updateGroup", "msdp_group")
  GUI.registerHandler("msdp.AFFECTS", "GUI.updateAffectIcons", "msdp_affects")
  GUI.registerHandler("sysProtocolEnabled", "GUI.onProtocolEnabled", "sys_protocol")
  GUI.registerHandler("msdp.HEALTH", "GUI.updateHealthGauge", "msdp_health")
  GUI.registerHandler("msdp.HEALTH_MAX", "GUI.updateHealthGauge", "msdp_health_max")
  GUI.registerHandler("msdp.OPPONENT_HEALTH", "GUI.updateEnemyGauge", "msdp_opp_health")
  GUI.registerHandler("msdp.OPPONENT_HEALTH_MAX", "GUI.updateEnemyGauge", "msdp_opp_health_max")
  GUI.registerHandler("msdp.OPPONENT_NAME", "GUI.updateEnemyGauge", "msdp_opp_name")
  GUI.registerHandler("msdp.MOVEMENT", "GUI.updateMovesGauge", "msdp_movement")
  GUI.registerHandler("msdp.MOVEMENT_MAX", "GUI.updateMovesGauge", "msdp_movement_max")
  GUI.registerHandler("msdp.EXPERIENCE", "GUI.updateExperienceGauge", "msdp_exp")
  GUI.registerHandler("msdp.EXPERIENCE_TNL", "GUI.updateExperienceGauge", "msdp_exp_tnl")
  GUI.registerHandler("msdp.EXPERIENCE_MAX", "GUI.updateExperienceGauge", "msdp_exp_max")
  GUI.registerHandler("msdp.ACTIONS", "GUI.updateActionIcons", "msdp_actions")
  GUI.registerHandler("msdp.CHARACTER_NAME", "GUI.updatePlayer", "msdp_char_name")
  GUI.registerHandler("msdp.POSITION", "GUI.updatePlayer", "msdp_position")
  GUI.registerHandler("msdp.RACE", "GUI.updatePlayer", "msdp_race")
  GUI.registerHandler("msdp.CLASS", "GUI.updatePlayer", "msdp_class")
  GUI.registerHandler("msdp.ALIGNMENT", "GUI.updatePlayer", "msdp_alignment")
  GUI.registerHandler("msdp.LEVEL", "GUI.updatePlayer", "msdp_level")
  GUI.registerHandler("msdp.STR", "GUI.updatePlayer", "msdp_str")
  GUI.registerHandler("msdp.DEX", "GUI.updatePlayer", "msdp_dex")
  GUI.registerHandler("msdp.CON", "GUI.updatePlayer", "msdp_con")
  GUI.registerHandler("msdp.INT", "GUI.updatePlayer", "msdp_int")
  GUI.registerHandler("msdp.WIS", "GUI.updatePlayer", "msdp_wis")
  GUI.registerHandler("msdp.CHA", "GUI.updatePlayer", "msdp_cha")
  GUI.registerHandler("msdp.AC", "GUI.updatePlayer", "msdp_ac")
  GUI.registerHandler("msdp.MONEY", "GUI.updatePlayer", "msdp_money")
  GUI.registerHandler("msdp.ROOM", "GUI.updateRoom", "msdp_room")
  
  -- Toggle handlers
  GUI.registerHandler("sysLoadEvent", "GUI.loadToggles", "sys_load_toggles")
  GUI.registerHandler("sysExitEvent", "GUI.saveToggles", "sys_exit_toggles")
  
  -- Main init handlers
  GUI.registerHandler("sysLoadEvent", "GUI.init", "sys_load_init")
  GUI.registerHandler("sysInstall", "GUI.init", "sys_install_init")
  
  cecho("&lt;green&gt;[Resource Cleanup] Event handler migration complete\n")
end
</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Custom Scrollbar</name>
						<packageName/>
						<script>function GUI.styleScrollbar()
  local background_color = &quot;#202020&quot;
  local border_color = &quot;#515151&quot;
  setAppStyleSheet(
    [[
  QScrollBar:vertical {
     background: ]] ..
    background_color ..
    [[;
     width: 15px;
     margin: 22px 0 22px 0;
  }
  QScrollBar::handle:vertical {
     background-color: ]] ..
    background_color ..
    [[;
     min-height: 20px;
     border-width: 2px;
     border-style: solid;
     border-color: ]] ..
    border_color ..
    [[;
     border-radius: 7px;
  }
  QScrollBar::add-line:vertical {
   background-color: ]] ..
    background_color ..
    [[;
   border-width: 2px;
   border-style: solid;
   border-color: ]] ..
    border_color ..
    [[;
   border-bottom-left-radius: 7px;
   border-bottom-right-radius: 7px;
        height: 15px;
        subcontrol-position: bottom;
        subcontrol-origin: margin;
  }
  QScrollBar::sub-line:vertical {
   background-color: ]] ..
    background_color ..
    [[;
   border-width: 2px;
   border-style: solid;
   border-color: ]] ..
    border_color ..
    [[;
   border-top-left-radius: 7px;
   border-top-right-radius: 7px;
        height: 15px;
        subcontrol-position: top;
        subcontrol-origin: margin;
  }
  QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
     background: white;
     width: 4px;
     height: 3px;
  }
  QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
     background: none;
  }
]]
  )
end</script>
						<eventHandlerList/>
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Delete Line and  Prompt</name>
						<packageName/>
						<script>function deleteLineP()
  deleteLine()
  tempLineTrigger(1, 1, [[if isPrompt() then deleteLine() end]])
end</script>
						<eventHandlerList/>
					</Script>
				</ScriptGroup>
			</ScriptGroup>
			<ScriptGroup isActive="yes" isFolder="yes">
				<name>YATCOConfig</name>
				<packageName>YATCOConfig</packageName>
				<script/>
				<eventHandlerList/>
				<ScriptGroup isActive="yes" isFolder="yes">
					<name>YATCOCONFIG</name>
					<packageName/>
					<script>demonnic = demonnic or {}
demonnic.chat = demonnic.chat or {}
demonnic.chat.config = demonnic.chat.config or {}</script>
					<eventHandlerList/>
					<Script isActive="yes" isFolder="no">
						<name>Configuration Options</name>
						<packageName/>
						<script>--[[
This is where all of the configuration options can be set.
Anything I've put in this script object can be changed, but please do pay attention to what you're doing.
If you change one of the values to something it shouldn't be, you could break it.
]]
--This is where you tell it to use tabbed chat.
demonnic.chat.use = true
--[[
This is where you put what container to put the tabbed chat stuff into. Make it
equal to the actual container object you want it in, or false for none. Defaults to false
Which is to say if you want to put the tabbed chat stuff into a container made using
uiRight = Geyser.Container:new()
you would put
demonnic.chat.useContainer = uiRight
and if you don't want it in a container you would put
demonnic.chat.useContainer = false
]]
demonnic.chat.useContainer = GUI.chatContainer
--[[
The timestamp option is set here.
Set to false if you do not want any timestamps displayed for chat.
If you do want it displayed, set to the string for the format you wish.
see http://wiki.mudlet.org/w/Manual:Lua_Functions#getTime for information
how to format the string
]]
--demonnic.chat.config.timestamp = &quot;HH:mm:ss&quot;
demonnic.chat.config.timestamp = false
--[[ Should we use our own colors for the timestamp?
Set to true if you want to specify foreground and background colors
for the timestamp.
Set to false if you want the timestamps background and foreground
colors to match that of the mud output.
]]
demonnic.chat.config.timestampCustomColor = false
--[[
and what foreground color? You can either use one of the 'named' colors
(see http://wiki.mudlet.org/images/c/c3/ShowColors.png for available colors)
demonnic.chat.config.timestampFG = &quot;slate_grey&quot;
Or you can use a table of R,G,B values.
demonnic.chat.config.timestampFG = {
  255,
    0,
    0,
}
then the foreground for the timestamp would be 255 read, 100 green, and 0 blue
]]
demonnic.chat.config.timestampFG = &quot;red&quot;
--and background? Same rules as for the foreground above
demonnic.chat.config.timestampBG = &quot;blue&quot;
--[[
This is where you say what corner of the screen you want the tabbed chat on
Valid settings are &quot;topright&quot;, &quot;topleft&quot;, &quot;bottomright&quot;, &quot;bottomleft&quot;
]]--
demonnic.chat.config.location = &quot;topright&quot;
--[[
This is a table of channels you would like.
AKA the place you tell the script what tabs you want.
Each entry must be a string. The defaults should be a pretty effective guide.
]]
demonnic.chat.config.channels = {
  &quot;All&quot;,
  &quot;Tell&quot;,
  &quot;Chat&quot;,
	&quot;Wiz&quot;,
	&quot;Group&quot;,
	&quot;Congrats&quot;,
	&quot;Auction&quot;,
}
--Set this to the name of the channel you want to have everything sent to.
--Per the default, this would be the &quot;All&quot; channel. If you have a different name for it:
--
--demonnic.chat.config.Alltab = &quot;Bucket&quot;
--
--And if you don't want it turned on at all:
--
--demonnic.chat.config.Alltab = false
demonnic.chat.config.Alltab = &quot;All&quot;
--Set this to the name of the channel you want to display your map. Set to &quot;&quot; if you
--don't want to display the map in your YATCO tabs
demonnic.chat.config.Maptab = &quot;&quot;
---------------------------------------------------------------------------------
--                                                                             --
--The infamous blinking stuff!!!                                               --
--                                                                             --
---------------------------------------------------------------------------------
--[[
Do you want tabs to blink when you get new messages, until you click on the tab?
True if yes, false if no.
]]
demonnic.chat.config.blink = true
--How long (in seconds) between blinks? For example, 1 would mean a 1 second pause in between blinks.
demonnic.chat.config.blinkTime = 3
--Blink if the bucket tab (&quot;All&quot; by default, but configured above) is in focus?
demonnic.chat.config.blinkFromAll = false
--Font size for the chat messages
demonnic.chat.config.fontSize = getFontSize(&quot;main&quot;)
--[[
Should we preserve the formatting of the text.
Or should we set the background of it to match the window color?
Set this to false if you want the background for all chat to match the background of the window.
Useful if you change the background from black, and don't like the way the pasted chat makes blocks in it
]]
demonnic.chat.config.preserveBackground = true
--[[
Gag the chat lines in the main window?
defaults to false, set to true if you want to gag.
]]
demonnic.chat.config.gag = false
--[[
Number of lines of chat visible at once.
Will determine how tall the window for the chats is.
]]
demonnic.chat.config.lines = 22
--[[
Number of characters to wrap the chatlines at.
This will also determine how wide the chat windows are.
]]
demonnic.chat.config.width = getColumnCount(&quot;main&quot;)
--[[
Set the color for the active tab. R,G,B format.
The default here is a better blue for readability
]]
demonnic.chat.config.activeColors = {
  r = 50,
  g = 100,
  b = 150,
}
--[[
Set the color for the inactive tab. R,G,B format.
The default here is a more readable dark grey
]]
demonnic.chat.config.inactiveColors = {
  r = 70,
  g = 70,
  b = 70,
}
--[[
Set the color for the chat window itself. R,G,B format.
Defaulted to the black of my twisted hardened soul. Or something.
]]
demonnic.chat.config.windowColors = {
  r = 0,
  g = 0,
  b = 0,
}
--[[
Set the color for the text on the active tab. Uses color names.
Set the default to white for better contrast with the blue background.
]]
demonnic.chat.config.activeTabText = &quot;white&quot;
--[[
Set the color for the text on the inactive tabs. Uses color names.
Defaulted this to white. So the tabs you're not looking at will be white text on boring grey background.
]]
demonnic.chat.config.inactiveTabText = &quot;white&quot;
--[[
have to make sure a currentTab is set...
so we'll use the one for the bucket, or the first one in the channels table
Or, you know... what it's currently set to, if it's already set.
]]
demonnic.chat.currentTab = demonnic.chat.currentTab or demonnic.chat.config.Alltab or demonnic.chat.config.channels[1]
</script>
						<eventHandlerList/>
					</Script>
				</ScriptGroup>
			</ScriptGroup>
			<ScriptGroup isActive="yes" isFolder="yes">
				<name>YATCO</name>
				<packageName>YATCO</packageName>
				<script/>
				<eventHandlerList/>
				<ScriptGroup isActive="yes" isFolder="yes">
					<name>Demonnic</name>
					<packageName/>
					<script/>
					<eventHandlerList/>
					<ScriptGroup isActive="yes" isFolder="yes">
						<name>Shared</name>
						<packageName/>
						<script>--Bootstrapping variables/etc. Don't touch this unless you really know what you're doing
--I mean it. I'll point. AND laugh. loudly.
demonnic = demonnic or {}
demonnic.config = demonnic.config or {}
demonnic.balances = demonnic.balances or {}
demonnic.balances.balance = demonnic.balances.balance or 1
demonnic.balances.equilibrium = demonnic.balances.equilibrium or 1
demonnic.debug = demonnic.debug or {}
demonnic.debug.active = demonnic.debug.active or nil
demonnic.debug.categories = demonnic.debug.categories or { }
function demonnic:echo(msg)
 cecho(string.format(&quot;\n&lt;blue&gt;(&lt;green&gt;Demonnic&lt;blue&gt;):&lt;white&gt; %s&quot;, msg))
end</script>
						<eventHandlerList/>
						<Script isActive="yes" isFolder="no">
							<name>Debugging</name>
							<packageName/>
							<script>--Adds debugging functionality
function demonnic:Debug(category,debugData)
   if category then
      if table.contains(demonnic.debug.categories, category) then
         if type(debugData) == &quot;table&quot; then
            demonnic:echo(&quot;&lt;red&gt;DEBUG &quot; .. category .. &quot;:&lt;white&gt;&quot;)
            display(debugData)
         elseif type(debugData) == &quot;string&quot; or type(debugData) == &quot;number&quot; then
            demonnic:echo(&quot;&lt;red&gt;DEBUG &quot; .. category .. &quot;:&lt;white&gt; &quot; .. debugData .. &quot;\n&quot; )
         else
            demonnic:echo(&quot;&lt;red&gt;DEBUG &quot; .. category .. &quot;:&lt;white&gt; &quot; .. tostring(debugData) .. &quot;\n&quot; )
         end
      end
   else
      if type(debugData) == &quot;table&quot; then
         demonnic:echo(&quot;&lt;red&gt;DEBUG:&lt;white&gt;&quot;)
         display(debugData)
      elseif type(debugData) == &quot;string&quot; or type(debugData) == &quot;number&quot; then
         demonnic:echo(&quot;&lt;red&gt;DEBUG:&lt;white&gt; &quot; .. debugData)
      else
         demonnic:echo(&quot;&lt;red&gt;DEBUG:&lt;white&gt; &quot; .. tostring(debugData))
      end
   end
end
function demonnic:printDebug(category, debugData)
   if not demonnic.debug.active then return end
   demonnic:Debug(category, debugData)
end
function demonnic:toggleDebug()
   if demonnic.debug.active then demonnic.debug.active = nil
   else demonnic.debug.active = true
   end
   demonnic:echo(&quot;Debugging is currently &quot; .. (( demonnic.debug.active and &quot;&lt;green&gt;ON&lt;white&gt;&quot;) or &quot;&lt;red&gt;OFF&lt;white&gt;&quot;))
end
function demonnic:watchCategory( category )
   if table.contains(demonnic.debug.categories, category) then
      for i,v in ipairs(demonnic.debug.categories) do
         if v == category then
            table.remove(demonnic.debug.categories, i)
         end
      end
      demonnic:echo(&quot;No longer watching the '&lt;red&gt;&quot;..category..&quot;&lt;white&gt;' category.&quot;)
   else
      table.insert(demonnic.debug.categories, category)
      demonnic:echo(&quot;Now watching the '&lt;red&gt;&quot;..category..&quot;&lt;white&gt;' category.&quot;)
   end
   demonnic:echo(&quot;Debugging is currently &quot; .. (( demonnic.debug.active and &quot;&lt;green&gt;ON&lt;white&gt;&quot;) or &quot;&lt;red&gt;OFF&lt;white&gt;&quot;))
end
function demonnic:listCategories()
   if #demonnic.debug.categories &gt; 0 then
      demonnic:echo(&quot;You are currently watching the following categories:\n&quot; .. table.concat(demonnic.debug.categories,&quot;, &quot;) )
   else
      demonnic:echo(&quot;You are not watching any debugs.&quot;)
   end
end
</script>
							<eventHandlerList/>
						</Script>
						<Script isActive="yes" isFolder="no">
							<name>Geyser Additions</name>
							<packageName/>
							<script>function Geyser.MiniConsole:clear()
   clearWindow(self.name)
end
function Geyser.MiniConsole:append()
  appendBuffer(self.name)
end</script>
							<eventHandlerList/>
						</Script>
					</ScriptGroup>
					<ScriptGroup isActive="yes" isFolder="yes">
						<name>Tabbed Chat</name>
						<packageName/>
						<script>--Do not remove the following lines. Or change them.
demonnic = demonnic or {}
demonnic.chat = demonnic.chat or {}
demonnic.chat.tabsToBlink = demonnic.chat.tabsToBlink or {}
demonnic.chat.tabs = demonnic.chat.tabs or {}
demonnic.chat.windows = demonnic.chat.windows or {}
if not demonnic.chat.config then
  cecho(&quot;&lt;red:white&gt;YOU DO NOT HAVE THE YATCO CONFIG PACKAGE IN PLACE. THINGS WILL NOT WORK AS EXPECTED\n\n&quot;)
  demonnic.chat.error = &quot;NO CONFIG&quot;
end</script>
						<eventHandlerList/>
						<Script isActive="yes" isFolder="no">
							<name>Code</name>
							<packageName/>
							<script>--[[
If the label callbacks ever decide to start taking a function which is part of a table, 0then this will change.
Or if it's modified to take actual functions. Anonymouse function clickcallback would be awfully nice.
]]
function demonnicChatSwitch(chat)
  local r = demonnic.chat.config.inactiveColors.r
  local g = demonnic.chat.config.inactiveColors.g
  local b = demonnic.chat.config.inactiveColors.b
  local newr = demonnic.chat.config.activeColors.r
  local newg = demonnic.chat.config.activeColors.g
  local newb = demonnic.chat.config.activeColors.b
  local oldchat = demonnic.chat.currentTab
  if demonnic.chat.currentTab ~= chat then
    demonnic.chat.windows[oldchat]:hide()
    demonnic.chat.tabs[oldchat]:setColor(r, g, b)
    demonnic.chat.tabs[oldchat]:echo(oldchat, demonnic.chat.config.inactiveTabText, &quot;c&quot;)
    if demonnic.chat.config.blink and demonnic.chat.tabsToBlink[chat] then
      demonnic.chat.tabsToBlink[chat] = nil
    end
    if demonnic.chat.config.blink and chat == demonnic.chat.config.Alltab then
      demonnic.chat.tabsToBlink = {}
    end
  end
  demonnic.chat.tabs[chat]:setColor(newr, newg, newb)
  demonnic.chat.tabs[chat]:echo(chat, demonnic.chat.config.activeTabText, &quot;c&quot;)
  demonnic.chat.windows[chat]:show()
  demonnic.chat.currentTab = chat
end
function demonnic.chat:resetUI()
  demonnic.chat.container =
  demonnic.chat.useContainer or Geyser.Container:new(demonnic.chat[demonnic.chat.config.location]())
  demonnic.chat.tabBox =
  Geyser.HBox:new(
  {
    x = 0,
    y = 0,
    width = &quot;100%&quot;,
    height = &quot;25px&quot;,
    name = &quot;DemonChatTabs&quot;
  },
  demonnic.chat.container
  )
end
function demonnic.chat:create()
  --reset the UI
  demonnic.chat:resetUI()
  --Set some variables locally to increase readability
  local r = demonnic.chat.config.inactiveColors.r
  local g = demonnic.chat.config.inactiveColors.g
  local b = demonnic.chat.config.inactiveColors.b
  local winr = demonnic.chat.config.windowColors.r
  local wing = demonnic.chat.config.windowColors.g
  local winb = demonnic.chat.config.windowColors.b
  --iterate the table of channels and create some windows and tabs
  for i, tab in ipairs(demonnic.chat.config.channels) do
    demonnic.chat.tabs[tab] =
    Geyser.Label:new(
    {
      name = string.format(&quot;tab%s&quot;, tab)
    },
    demonnic.chat.tabBox
    )
    demonnic.chat.tabs[tab]:echo(tab, demonnic.chat.config.inactiveTabText, &quot;c&quot;)
    demonnic.chat.tabs[tab]:setColor(r, g, b)
    demonnic.chat.tabs[tab]:setClickCallback(&quot;demonnicChatSwitch&quot;, tab)
    demonnic.chat.tabs[tab]:setStyleSheet(
    [[font-family: Tahoma, Geneva, sans-serif;
    border-image: url(]] ..
    getMudletHomeDir():gsub(&quot;\\&quot;, &quot;/&quot;) ..
    [[/LuminariGUI/images/buttons/button.png) 0 0 0 0 stretch stretch;]]
    )
    demonnic.chat.windows[tab] =
    Geyser.MiniConsole:new(
    {
      --      fontSize = demonnic.chat.config.fontSize,
      x = 0,
      y = 25,
      height = &quot;100%&quot;,
      width = &quot;100%&quot;,
      name = string.format(&quot;win%s&quot;, tab)
    },
    demonnic.chat.container
    )
    demonnic.chat.windows[tab]:setFontSize(demonnic.chat.config.fontSize)
    demonnic.chat.windows[tab]:setFont(getFont(&quot;main&quot;))
    demonnic.chat.windows[tab]:setColor(winr, wing, winb)
    demonnic.chat.windows[tab]:setWrap(demonnic.chat.config.width)
    demonnic.chat.windows[tab]:hide()
  end
  if demonnic.chat.config.Maptab and demonnic.chat.config.Maptab ~= &quot;&quot; then
    demonnic.chat.mapWindow =
    Geyser.Mapper:new(
    {
      x = 0,
      y = 0,
      height = &quot;100%&quot;,
      width = &quot;100%&quot;
    },
    demonnic.chat.windows[demonnic.chat.config.Maptab]
    )
    demonnic.chat.windows[demonnic.chat.config.Maptab]:hide()
  end
  local showme = demonnic.chat.config.Alltab or demonnic.chat.config.channels[1]
  demonnicChatSwitch(showme)
  --start the blink timers, if enabled
  if demonnic.chat.config.blink and not demonnic.chat.blinkTimerOn then
    demonnic.chat:blink()
  end
end
function demonnic.chat:append(chat)
  local r = demonnic.chat.config.windowColors.r
  local g = demonnic.chat.config.windowColors.g
  local b = demonnic.chat.config.windowColors.b
  selectCurrentLine()
  local ofr, ofg, ofb = getFgColor()
  local obr, obg, obb = getBgColor()
  if demonnic.chat.config.preserveBackground then
    setBgColor(r, g, b)
  end
  copy()
  if demonnic.chat.config.timestamp then
    local timestamp = getTime(true, demonnic.chat.config.timestamp)
    local tsfg = {}
    local tsbg = {}
    local colorLeader = &quot;&quot;
    if demonnic.chat.config.timestampCustomColor then
      if type(demonnic.chat.config.timestampFG) == &quot;string&quot; then
        tsfg = color_table[demonnic.chat.config.timestampFG]
      else
        tsfg = demonnic.chat.config.timestampFG
      end
      if type(demonnic.chat.config.timestampBG) == &quot;string&quot; then
        tsbg = color_table[demonnic.chat.config.timestampBG]
      else
        tsbg = demonnic.chat.config.timestampBG
      end
      colorLeader = string.format(&quot;&lt;%s,%s,%s:%s,%s,%s&gt;&quot;, tsfg[1], tsfg[2], tsfg[3], tsbg[1], tsbg[2], tsbg[3])
    else
      colorLeader = string.format(&quot;&lt;%s,%s,%s:%s,%s,%s&gt;&quot;, ofr, ofg, ofb, obr, obg, obb)
    end
    local fullstamp = string.format(&quot;%s%s&quot;, colorLeader, timestamp)
    demonnic.chat.windows[chat]:decho(fullstamp)
    demonnic.chat.windows[chat]:echo(&quot; &quot;)
    if demonnic.chat.config.Alltab then
      demonnic.chat.windows[demonnic.chat.config.Alltab]:decho(fullstamp)
      demonnic.chat.windows[demonnic.chat.config.Alltab]:echo(&quot; &quot;)
    end
  end
  demonnic.chat.windows[chat]:append()
  if demonnic.chat.config.gag then
    deleteLine()
    tempLineTrigger(1, 1, [[if isPrompt() then deleteLine() end]])
  end
  if demonnic.chat.config.Alltab then
    appendBuffer(string.format(&quot;win%s&quot;, demonnic.chat.config.Alltab))
  end
  if demonnic.chat.config.blink and chat ~= demonnic.chat.currentTab then
    if (demonnic.chat.config.Alltab == demonnic.chat.currentTab) and not demonnic.chat.config.blinkOnAll then
      return
    else
      demonnic.chat.tabsToBlink[chat] = true
    end
  end
end
function demonnic.chat:blink()
  if demonnic.chat.blinkID then
    killTimer(demonnic.chat.blinkID)
  end
  if not demonnic.chat.config.blink then
    demonnic.chat.blinkTimerOn = false
    return
  end
  if not demonnic.chat.container.hidden then
    for tab, _ in pairs(demonnic.chat.tabsToBlink) do
      demonnic.chat.tabs[tab]:flash()
    end
  end
  -- Use tracked timer if available
  if GUI and GUI.createTimer then
    demonnic.chat.blinkID = GUI.createTimer(
      demonnic.chat.config.blinkTime,
      function () demonnic.chat:blink() end,
      false,
      &quot;yatco_blink&quot;
    )
  else
    demonnic.chat.blinkID =
    tempTimer(
    demonnic.chat.config.blinkTime,
    function ()
      demonnic.chat:blink()
    end
    )
  end
end
function demonnic.chat:topright()
  return {
    fontSize = demonnic.chat.config.fontSize,
    x = string.format(&quot;-%sc&quot;, demonnic.chat.config.width + 2),
    y = 0,
    width = &quot;-15px&quot;,
    height = string.format(&quot;%ic&quot;, demonnic.chat.config.lines + 2)
  }
end
function demonnic.chat:topleft()
  return {
    fontSize = demonnic.chat.config.fontSize,
    x = 0,
    y = 0,
    width = string.format(&quot;%sc&quot;, demonnic.chat.config.width),
    height = string.format(&quot;%ic&quot;, demonnic.chat.config.lines + 2)
  }
end
function demonnic.chat:bottomright()
  return {
    fontSize = demonnic.chat.config.fontSize,
    x = string.format(&quot;-%sc&quot;, demonnic.chat.config.width + 2),
    y = string.format(&quot;-%sc&quot;, demonnic.chat.config.lines + 2),
    width = &quot;-15px&quot;,
    height = string.format(&quot;%ic&quot;, demonnic.chat.config.lines + 2)
  }
end
function demonnic.chat:bottomleft()
  return {
    fontSize = demonnic.chat.config.fontSize,
    x = 0,
    y = string.format(&quot;-%sc&quot;, demonnic.chat.config.lines + 2),
    width = string.format(&quot;%sc&quot;, demonnic.chat.config.width),
    height = string.format(&quot;%ic&quot;, demonnic.chat.config.lines + 2)
  }
end
</script>
							<eventHandlerList/>
						</Script>
						<Script isActive="yes" isFolder="no">
							<name>demonnicOnStart</name>
							<packageName/>
							<script>function demonnicOnStart()
		if demonnic.chat.use then
				cecho(&quot;&lt;cyan&gt;[DEBUG] Starting YATCO initialization timer (0.5s delay)...\n&quot;)
				-- Delay chat creation to ensure GUI containers are ready
				local timerFunc = function ()
						cecho(&quot;&lt;cyan&gt;[DEBUG] YATCO timer fired - checking for conflicts...\n&quot;)
						
						-- DEBUG: Check cast console state before YATCO init
						if GUI.castConsole then
							 cecho(&quot;&lt;yellow&gt;[DEBUG] Cast console exists before YATCO init: visible=&quot; .. tostring(not GUI.castConsole.hidden) .. &quot;\n&quot;)
						else
							 cecho(&quot;&lt;red&gt;[DEBUG] Cast console missing before YATCO init!\n&quot;)
						end
						
						if GUI.chatContainer then
							 cecho(&quot;&lt;cyan&gt;[DEBUG] Installing YATCO chat system...\n&quot;)
							 demonnic.chat:create()
							 cecho(&quot;&lt;cyan&gt;YATCO chat system initialized.\n&quot;)
							 
							 -- DEBUG: Check cast console state after YATCO init
							 if GUI.castConsole then
							   cecho(&quot;&lt;yellow&gt;[DEBUG] Cast console exists after YATCO init: visible=&quot; .. tostring(not GUI.castConsole.hidden) .. &quot;\n&quot;)
							 else
							   cecho(&quot;&lt;red&gt;[DEBUG] Cast console missing after YATCO init!\n&quot;)
							 end
						else
							 cecho(&quot;&lt;red&gt;Error: GUI.chatContainer not found. Chat system not initialized.\n&quot;)
						end
				end
				
				-- Use tracked timer if available
				if GUI and GUI.createTimer then
					GUI.yatcoInitTimer = GUI.createTimer(0.5, timerFunc, false, &quot;yatco_init&quot;)
				else
					tempTimer(0.5, timerFunc)
				end
		end
end
-- Use tracked handler if available
if GUI and GUI.registerHandler then
  GUI.registerHandler(&quot;sysLoadEvent&quot;, &quot;demonnicOnStart&quot;, &quot;yatco_startup&quot;)
else
  registerAnonymousEventHandler(&quot;sysLoadEvent&quot;, &quot;demonnicOnStart&quot;)
end</script>
							<eventHandlerList>
								<string>sysLoadEvent</string>
							</eventHandlerList>
						</Script>
						<Script isActive="yes" isFolder="no">
							<name>echo functions</name>
							<packageName/>
							<script>
function demonnic.chat:cecho(chat, message)
  local alltab = demonnic.chat.config.Alltab
  local blink = demonnic.chat.config.blink
  cecho(string.format(&quot;win%s&quot;,chat), message)
  if alltab and chat ~= alltab then
    cecho(string.format(&quot;win%s&quot;, alltab), message)
  end
  if blink and chat ~= demonnic.chat.currentTab then
    if (alltab == demonnic.chat.currentTab) and not demonnic.chat.config.blinkOnAll then
      return
    else
      demonnic.chat.tabsToBlink[chat] = true
    end
  end
end
function demonnic.chat:decho(chat, message)
  local alltab = demonnic.chat.config.Alltab
  local blink = demonnic.chat.config.blink
  decho(string.format(&quot;win%s&quot;,chat), message)
  if alltab and chat ~= alltab then
    decho(string.format(&quot;win%s&quot;, alltab), message)
  end
  if blink and chat ~= demonnic.chat.currentTab then
    if (alltab == demonnic.chat.currentTab) and not demonnic.chat.config.blinkOnAll then
      return
    else
      demonnic.chat.tabsToBlink[chat] = true
    end
  end
end
function demonnic.chat:hecho(chat, message)
  local alltab = demonnic.chat.config.Alltab
  local blink = demonnic.chat.config.blink
  hecho(string.format(&quot;win%s&quot;,chat), message)
  if alltab and chat ~= alltab then
    hecho(string.format(&quot;win%s&quot;, alltab), message)
  end
  if blink and chat ~= demonnic.chat.currentTab then
    if (alltab == demonnic.chat.currentTab) and not demonnic.chat.config.blinkOnAll then
      return
    else
      demonnic.chat.tabsToBlink[chat] = true
    end
  end
end
function demonnic.chat:echo(chat, message)
  local alltab = demonnic.chat.config.Alltab
  local blink = demonnic.chat.config.blink
  echo(string.format(&quot;win%s&quot;,chat), message)
  if alltab and chat ~= alltab then
    echo(string.format(&quot;win%s&quot;, alltab), message)
  end
  if blink and chat ~= demonnic.chat.currentTab then
    if (alltab == demonnic.chat.currentTab) and not demonnic.chat.config.blinkOnAll then
      return
    else
      demonnic.chat.tabsToBlink[chat] = true
    end
  end
end</script>
							<eventHandlerList/>
						</Script>
						<Script isActive="yes" isFolder="no">
							<name>demonnicOnInstall</name>
							<packageName/>
							<script>function demonnicOnInstall(_, package)
  if package:find(&quot;YATCO&quot;) then
  	demonnicOnStart()
  end
end</script>
							<eventHandlerList>
								<string>sysInstall</string>
							</eventHandlerList>
						</Script>
					</ScriptGroup>
				</ScriptGroup>
			</ScriptGroup>
			<Script isActive="no" isFolder="no">
				<name>Changelog</name>
				<packageName/>
				<script>--Changelog
--[[
   1.) Changed the following to support moving chatbox to bottom:
	   -Added GUI.Bottom to Create Background Script
	   -Changed bottom border in Set Borders Script from setBorderBottom(0) to setBorderBottom(h / 4)
	   -Now Utilizing GUI.Box2 fot chatbox at bottom.
	   -Rewrote createFrame() function to allow for automatic frame generating regardless of
		  box size. Using the function is no different.
	   -Adjusted chatContainer height to fit frame nicely.
	   -Adjusted chatContainer x and y to fit frame nicely.
	   -Changed GUI.chatContainer name from GUI.Box2 to GUI.chatContainer
	 2.) Added the following to support adding buttons to Box3
		 -Created a new Script named Button which handles buttons individually by row.
		 -Creating button drawing functions so that they will each be called in an init
		  alias to generate them row by row. This will make it easier to change buttons
		  in the future.
		 -Making Box3 smaller. We don't need as many buttons as we have room for and I
		  have ideas for new things to place below Box3!
		 -Box 5 and 6 created. 5 to be used for Legend/Room Info. 6 to be used for skill
		  cooldown icons.
		 -Buttons added in Buttons script. Callbacks there as well. Legend is a toggle and
		  Mudlet/ASCII are separate callbacks.
	     -Legend and room info script created in Room Info/Legend script and proper event
		  handlers added to config for room info.
		 -Added check to Capture Room Map trigger to only display ASCII map if
		  GUI.buttonWindow.mudletOrAscii == &quot;ASCII&quot;.
	 3.) Changed the following to support release this week.
	     -Commenting out box 6 draw frame function.
	 4.) Various Bug Fixes:
	     -Fixed the trigger for Group chat to capture others talking in group.
         -Fixed gauges so they can no longer overfill into the main buffer by adding:
          if pct_health &gt; 100 then
            GUI.Health:setValue(100, 100)
          else
            GUI.Health:setValue(pct_health, 100)
          end
          *Equivalent added for each gauge. May look to add an effect to show a bar
           is overfilled when an enemy rages in the future*
	 5.) Quality of life:
		 -Added a function to delete a line and the following prompt. It can be
		  utilized in any trigger to delete any line and the prompt it would have
		  created, but I created it primarily for the chatbox. Function is called
		  using deleteLineP().
		 -Added toggles that are saved to a .lua on exit and loaded on load event.
		  Currently they're used to determine if you're shown in the group or not
		  and if you would like to gag chat from main buffer with the above
		  function. (deleteLineP())
	     -
]]
</script>
				<eventHandlerList/>
			</Script>
		</ScriptGroup>
	</ScriptPackage>
	<KeyPackage/>
	<HelpPackage>
		<helpURL/>
	</HelpPackage>
</MudletPackage>
