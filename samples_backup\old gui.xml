<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE MudletPackage>
<MudletPackage version="1.001">
	<TriggerPackage>
		<TriggerGroup isActive="yes" isFolder="yes" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
			<name>LuminariGUI</name>
			<script></script>
			<triggerType>0</triggerType>
			<conditonLineDelta>0</conditonLineDelta>
			<mStayOpen>0</mStayOpen>
			<mCommand></mCommand>
			<packageName>LuminariGUI</packageName>
			<mFgColor>#ff0000</mFgColor>
			<mBgColor>#ffff00</mBgColor>
			<mSoundFile></mSoundFile>
			<colorTriggerFgColor>#000000</colorTriggerFgColor>
			<colorTriggerBgColor>#000000</colorTriggerBgColor>
			<regexCodeList />
			<regexCodePropertyList />
			<TriggerGroup isActive="yes" isFolder="yes" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
				<name>YATCOConfig</name>
				<script></script>
				<triggerType>0</triggerType>
				<conditonLineDelta>0</conditonLineDelta>
				<mStayOpen>0</mStayOpen>
				<mCommand></mCommand>
				<packageName>YATCOConfig</packageName>
				<mFgColor>#ff0000</mFgColor>
				<mBgColor>#ffff00</mBgColor>
				<mSoundFile></mSoundFile>
				<colorTriggerFgColor>#000000</colorTriggerFgColor>
				<colorTriggerBgColor>#000000</colorTriggerBgColor>
				<regexCodeList />
				<regexCodePropertyList />
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Tell</name>
					<script>demonnic.chat:append("Tell")

if GUI.toggles.gagChat == true then
  deleteLineP()
end</script>
					<triggerType>0</triggerType>
					<conditonLineDelta>39</conditonLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand></mCommand>
					<packageName></packageName>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile></mSoundFile>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>^(\w+) tells you, '(.*)'</string>
						<string>You tell </string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>1</integer>
						<integer>2</integer>
					</regexCodePropertyList>
				</Trigger>
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Congrats</name>
					<script>demonnic.chat:append("Congrats")

if GUI.toggles.gagChat == true then
  deleteLineP()
end</script>
					<triggerType>0</triggerType>
					<conditonLineDelta>0</conditonLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand></mCommand>
					<packageName></packageName>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile></mSoundFile>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>^(\w+) congrats, '(.*)</string>
						<string>You congrat, '</string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>1</integer>
						<integer>2</integer>
					</regexCodePropertyList>
				</Trigger>
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Chat</name>
					<script>demonnic.chat:append("Chat")

if GUI.toggles.gagChat == true then
  deleteLineP()
end</script>
					<triggerType>0</triggerType>
					<conditonLineDelta>39</conditonLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand></mCommand>
					<packageName></packageName>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile></mSoundFile>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>^(\w+) chats, '(.*)</string>
						<string>You chat, '</string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>1</integer>
						<integer>2</integer>
					</regexCodePropertyList>
				</Trigger>
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Auction</name>
					<script>demonnic.chat:append("Auction")

if GUI.toggles.gagChat == true then
  deleteLineP()
end</script>
					<triggerType>0</triggerType>
					<conditonLineDelta>0</conditonLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand></mCommand>
					<packageName></packageName>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile></mSoundFile>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>^(\w+) auctions, '(.*)</string>
						<string>You auction, '</string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>1</integer>
						<integer>2</integer>
					</regexCodePropertyList>
				</Trigger>
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Group</name>
					<script>demonnic.chat:append("Group")

if GUI.toggles.gagChat == true then
  deleteLineP()
end</script>
					<triggerType>0</triggerType>
					<conditonLineDelta>0</conditonLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand></mCommand>
					<packageName></packageName>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile></mSoundFile>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>[Group]</string>
						<string>You group-say, '</string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>2</integer>
						<integer>2</integer>
					</regexCodePropertyList>
				</Trigger>
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Wiznet</name>
					<script>demonnic.chat:append("Wiz")

if GUI.toggles.gagChat == true then
  deleteLineP()
end</script>
					<triggerType>0</triggerType>
					<conditonLineDelta>0</conditonLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand></mCommand>
					<packageName></packageName>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile></mSoundFile>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>[wiznet]</string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>2</integer>
					</regexCodePropertyList>
				</Trigger>
			</TriggerGroup>
			<TriggerGroup isActive="yes" isFolder="yes" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
				<name>GUI</name>
				<script></script>
				<triggerType>0</triggerType>
				<conditonLineDelta>0</conditonLineDelta>
				<mStayOpen>0</mStayOpen>
				<mCommand></mCommand>
				<packageName></packageName>
				<mFgColor>#ff0000</mFgColor>
				<mBgColor>#ffff00</mBgColor>
				<mSoundFile></mSoundFile>
				<colorTriggerFgColor>#000000</colorTriggerFgColor>
				<colorTriggerBgColor>#000000</colorTriggerBgColor>
				<regexCodeList />
				<regexCodePropertyList />
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Capture Wilderness Map</name>
					<script>deleteLine()
clearUserWindow("map.minimap")

map.container:hide()
map.minimapcontainer:show()

maplineTrig = tempLineTrigger(1,23,[[onMapLine()]])  -- Edit the max lines to how many your MAP display shows.
padding = map.calcMinimapPadding()

function onMapLine()
  -- Either match the last line of your MAP display, or use the Prompt.
  local isLastLine = string.findPattern(line, "&lt;/WILDERNESS_MAP&gt;")
  if isLastLine then
    deleteLine()
    killTrigger(maplineTrig)
  else
    selectCurrentLine()
    copy()
		for i = 1, padding do		
			map.minimap:echo(" ");
		end
		map.adjustMinimapFontSize()
    appendBuffer("map.minimap")
    deleteLine()
  end
end</script>
					<triggerType>0</triggerType>
					<conditonLineDelta>0</conditonLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand></mCommand>
					<packageName></packageName>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile></mSoundFile>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>&lt;WILDERNESS_MAP&gt;</string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>2</integer>
					</regexCodePropertyList>
				</Trigger>
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Capture Room Map</name>
					<script>deleteLine()
clearUserWindow("map.minimap")

if GUI.buttonWindow.mudletOrAscii == "ASCII" then
  map.container:hide()
  map.minimapcontainer:show()
elseif GUI.buttonWindow.mudletOrAscii == "Mudlet" then
  map.container:show()
  map.minimapcontainer:hide()
end
map.minimap:echo("\n")
maplineTrig = tempLineTrigger(1,11,[[onRoomMapLine()]])  -- Edit the max lines to how many your MAP display shows.
padding = map.calcAsciimapPadding()

function onRoomMapLine()
  -- Either match the last line of your MAP display, or use the Prompt.
  local isLastLine = string.findPattern(line, "&lt;/ROOM_MAP&gt;")
  if isLastLine then
    deleteLine()
    killTrigger(maplineTrig)
  else
    selectCurrentLine()
    copy()
		map.minimap:echo(" ")
		map.adjustAsciimapFontSize()
    appendBuffer("map.minimap")
    deleteLine()
  end
end</script>
					<triggerType>0</triggerType>
					<conditonLineDelta>0</conditonLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand></mCommand>
					<packageName></packageName>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile></mSoundFile>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>&lt;ROOM_MAP&gt;</string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>2</integer>
					</regexCodePropertyList>
				</Trigger>
				<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Gag blank lines</name>
					<script>deleteLine()</script>
					<triggerType>0</triggerType>
					<conditonLineDelta>0</conditonLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand></mCommand>
					<packageName></packageName>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile></mSoundFile>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList>
						<string>^$</string>
					</regexCodeList>
					<regexCodePropertyList>
						<integer>1</integer>
					</regexCodePropertyList>
				</Trigger>
				<TriggerGroup isActive="yes" isFolder="yes" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
					<name>Cast Console</name>
					<script></script>
					<triggerType>0</triggerType>
					<conditonLineDelta>0</conditonLineDelta>
					<mStayOpen>0</mStayOpen>
					<mCommand></mCommand>
					<packageName></packageName>
					<mFgColor>#ff0000</mFgColor>
					<mBgColor>#ffff00</mBgColor>
					<mSoundFile></mSoundFile>
					<colorTriggerFgColor>#000000</colorTriggerFgColor>
					<colorTriggerBgColor>#000000</colorTriggerBgColor>
					<regexCodeList />
					<regexCodePropertyList />
					<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
						<name>Started Cast</name>
						<script>GUI.castConsole_startCast(matches[2], matches[3])</script>
						<triggerType>0</triggerType>
						<conditonLineDelta>0</conditonLineDelta>
						<mStayOpen>0</mStayOpen>
						<mCommand></mCommand>
						<packageName></packageName>
						<mFgColor>#ff0000</mFgColor>
						<mBgColor>#ffff00</mBgColor>
						<mSoundFile></mSoundFile>
						<colorTriggerFgColor>#000000</colorTriggerFgColor>
						<colorTriggerBgColor>#000000</colorTriggerBgColor>
						<regexCodeList>
							<string>^Casting\: (.+) (\*+)$</string>
						</regexCodeList>
						<regexCodePropertyList>
							<integer>1</integer>
						</regexCodePropertyList>
					</Trigger>
					<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
						<name>Cast Complete</name>
						<script>GUI.castConsole_completeCast()</script>
						<triggerType>0</triggerType>
						<conditonLineDelta>0</conditonLineDelta>
						<mStayOpen>0</mStayOpen>
						<mCommand></mCommand>
						<packageName></packageName>
						<mFgColor>#ff0000</mFgColor>
						<mBgColor>#ffff00</mBgColor>
						<mSoundFile></mSoundFile>
						<colorTriggerFgColor>#000000</colorTriggerFgColor>
						<colorTriggerBgColor>#000000</colorTriggerBgColor>
						<regexCodeList>
							<string>You complete your spell...</string>
						</regexCodeList>
						<regexCodePropertyList>
							<integer>2</integer>
						</regexCodePropertyList>
					</Trigger>
					<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
						<name>Cast Aborted</name>
						<script>GUI.castConsole_abortedCast()</script>
						<triggerType>0</triggerType>
						<conditonLineDelta>0</conditonLineDelta>
						<mStayOpen>0</mStayOpen>
						<mCommand></mCommand>
						<packageName></packageName>
						<mFgColor>#ff0000</mFgColor>
						<mBgColor>#ffff00</mBgColor>
						<mSoundFile></mSoundFile>
						<colorTriggerFgColor>#000000</colorTriggerFgColor>
						<colorTriggerBgColor>#000000</colorTriggerBgColor>
						<regexCodeList>
							<string>You abort your spell.</string>
							<string>Your spell is aborted!</string>
							<string>You are unable to find the target for your spell!</string>
							<string>You are unable to find the object for your spell!</string>
							<string>You are unable to continue your spell in your current position!</string>
						</regexCodeList>
						<regexCodePropertyList>
							<integer>3</integer>
							<integer>3</integer>
							<integer>3</integer>
							<integer>3</integer>
							<integer>3</integer>
						</regexCodePropertyList>
					</Trigger>
					<Trigger isActive="yes" isFolder="no" isTempTrigger="no" isMultiline="no" isPerlSlashGOption="no" isColorizerTrigger="no" isFilterTrigger="no" isSoundTrigger="no" isColorTrigger="no" isColorTriggerFg="no" isColorTriggerBg="no">
						<name>Cast Canceled</name>
						<script>GUI.castConsole_canceledCast()</script>
						<triggerType>0</triggerType>
						<conditonLineDelta>0</conditonLineDelta>
						<mStayOpen>0</mStayOpen>
						<mCommand></mCommand>
						<packageName></packageName>
						<mFgColor>#ff0000</mFgColor>
						<mBgColor>#ffff00</mBgColor>
						<mSoundFile></mSoundFile>
						<colorTriggerFgColor>#000000</colorTriggerFgColor>
						<colorTriggerBgColor>#000000</colorTriggerBgColor>
						<regexCodeList>
							<string>You are unable to continue casting!</string>
							<string>You are too nauseated to continue casting!</string>
						</regexCodeList>
						<regexCodePropertyList>
							<integer>3</integer>
							<integer>3</integer>
						</regexCodePropertyList>
					</Trigger>
				</TriggerGroup>
			</TriggerGroup>
		</TriggerGroup>
	</TriggerPackage>
	<TimerPackage />
	<AliasPackage>
		<AliasGroup isActive="yes" isFolder="yes">
			<name>LuminariGUI</name>
			<script></script>
			<command></command>
			<packageName>LuminariGUI</packageName>
			<regex></regex>
			<AliasGroup isActive="yes" isFolder="yes">
				<name>Toggles</name>
				<script></script>
				<command></command>
				<packageName></packageName>
				<regex></regex>
				<Alias isActive="yes" isFolder="no">
					<name>Gag Chat</name>
					<script>--[[Toggling this will determine if the line and follow up
    prompt are removed from the main buffer for chat.]]
GUI.gagChatToggle()</script>
					<command></command>
					<packageName></packageName>
					<regex>^gag chat$</regex>
				</Alias>
				<Alias isActive="yes" isFolder="no">
					<name>Show Self</name>
					<script>--Toggles whether or not you are shown in the group tab.
GUI.showSelfToggle()</script>
					<command></command>
					<packageName></packageName>
					<regex>^show self$</regex>
				</Alias>
			</AliasGroup>
			<AliasGroup isActive="yes" isFolder="yes">
				<name>YATCO</name>
				<script></script>
				<command></command>
				<packageName>YATCO</packageName>
				<regex></regex>
				<AliasGroup isActive="yes" isFolder="yes">
					<name>Demonnic</name>
					<script></script>
					<command></command>
					<packageName></packageName>
					<regex></regex>
					<AliasGroup isActive="yes" isFolder="yes">
						<name>Shared</name>
						<script></script>
						<command></command>
						<packageName></packageName>
						<regex></regex>
						<Alias isActive="yes" isFolder="no">
							<name>Reset chasing</name>
							<script>demonnic.chaser:reset()</script>
							<command></command>
							<packageName></packageName>
							<regex>^chaseres$</regex>
						</Alias>
						<Alias isActive="yes" isFolder="no">
							<name>Debug</name>
							<script>if matches[2] then
  demonnic:listCategories()
else
  demonnic:toggleDebug()
end</script>
							<command></command>
							<packageName></packageName>
							<regex>^debug(?: (list))?$</regex>
						</Alias>
						<Alias isActive="yes" isFolder="no">
							<name>debug categories</name>
							<script>if matches[2] then
  demonnic:watchCategory( matches[2] )
else
  demonnic:listCategories()
end</script>
							<command></command>
							<packageName></packageName>
							<regex>^debugc(?: (.*))?$</regex>
						</Alias>
					</AliasGroup>
					<AliasGroup isActive="yes" isFolder="yes">
						<name>Tabbed Chat</name>
						<script></script>
						<command></command>
						<packageName></packageName>
						<regex></regex>
						<Alias isActive="yes" isFolder="no">
							<name>Toggle blinking (temporary change)</name>
							<script>if demonnic.chat.config.blink then
  demonnic.chat.config.blink = false
  demonnic.chat.tabsToBlink = {}
  demonnic:echo("Blinking temporarily turned &lt;red&gt;off&lt;grey&gt;. It will reset if you edit your tabbed chat configuration, or close and reopen mudlet. To make it permanent, change demonnic.chat.config.blink to false in \"Demonnic-&gt;Tabbed Chat-&gt;Configuration options\" under scripts\n")
else
  demonnic.chat.config.blink = true
  demonnic.chat:blink()
  demonnic:echo("Blinking temporarily turned &lt;red&gt;on&lt;grey&gt;. It will reset if you edit your tabbed chat configuration, or close and reopen mudlet. To make it permanent, change demonnic.chat.config.blink to true in \"Demonnic-&gt;Tabbed Chat-&gt;Configuration options\" under scripts\n")
end</script>
							<command></command>
							<packageName></packageName>
							<regex>^dblink$</regex>
						</Alias>
						<Alias isActive="yes" isFolder="no">
							<name>fixChat</name>
							<script>local currentsetting = demonnic.chat.config.location
local newsetting = ""
if currentsetting == "topright" then 
  newsetting = "bottomleft" 
elseif currentsetting == "topleft" then
  newsetting = "bottomright"
elseif currentsetting == "bottomleft" then
  newsetting = "topright"
elseif currentsetting == "bottomright" then
  newsetting = "topleft"
end

demonnic.chat.config.location = newsetting
demonnic.chat:create()
demonnic.chat.config.location = currentsetting
demonnic.chat:create()</script>
							<command></command>
							<packageName></packageName>
							<regex>^fixchat$</regex>
						</Alias>
					</AliasGroup>
				</AliasGroup>
			</AliasGroup>
		</AliasGroup>
	</AliasPackage>
	<ActionPackage />
	<ScriptPackage>
		<ScriptGroup isActive="yes" isFolder="yes">
			<name>LuminariGUI</name>
			<packageName>LuminariGUI</packageName>
			<script></script>
			<eventHandlerList />
			<ScriptGroup isActive="yes" isFolder="yes">
				<name>MSDPMapper</name>
				<packageName>Generic Mapper</packageName>
				<script></script>
				<eventHandlerList />
				<Script isActive="yes" isFolder="no">
					<name>MSDPMapper</name>
					<packageName></packageName>
					<script>
map = map or {}
map.room_info = map.room_info or {}
map.prev_info = map.prev_info or {}
map.aliases = map.aliases or {}
map.enabled = false
map.minimap_font_size = 8
map.minimap_width = 21
map.minimap_height = 21


local defaults = {
    -- using Geyser to handle the mapper in this, since this is a totally new script
    mapper = {x = "-25%", y = "0%", width = "25%", height = "50%"}
}

local terrain_types = {
    -- used to make rooms of different terrain types have different colors
    -- add a new entry for each terrain type, and set the color with RGB values
    -- each id value must be unique, terrain types not listed here will use mapper default color
    ["Inside"]         	= {id = 1, r = 130, g = 130, b = 130},
		["City"]           	= {id = 2, r = 200, g = 200, b = 200},
		["Field"] 					= {id = 3, r = 0, g = 170, b = 0},
		["Forest"] 					= {id = 4, r = 0, g = 122, b = 0},
		["Hills"] 					= {id = 5, r = 122, g = 69, b = 0},
		["Low Mountains"] 	= {id = 6, r = 100, g = 100, b = 100},
		["Water (Swim)"] 		= {id = 7, r = 0, g = 0, b = 255},
		["Water (No Swim)"] = {id = 8, r = 0, g = 0, b = 130},
		["In Flight"] 			= {id = 9, r = 200, g = 200, b = 255},
		["Underwater"] 			= {id = 10, r = 43, g = 43, b = 124},
		["Zone Entrance"] 	= {id = 211, r = 255, g = 0, b = 0},
		["Road North-South"] = {id = 12, r = 119, g = 101, b = 86},
		["Road East-West"] 	= {id = 13, r = 119, g = 101, b = 86},
		["Road Intersection"] = {id = 14, r = 119, g = 101, b = 86},
		["Desert"] 					= {id = 15, r = 234, g = 219, b = 124},
		["Ocean"] 					= {id = 16, r = 0, g = 90, b = 90},
		["Marshland"] 			= {id = 17, r = 81, g = 47, b = 109},
		["High Mountains"] 	= {id = 18, r = 255, g = 255, b = 255},
		["Outer Planes"] 		= {id = 19, r = 168, g = 42, b = 138},
		["Underdark - Wild"] = {id = 20, r = 131, g = 110, b = 145},
		["Underdark - City"] = {id = 21, r = 183, g = 178, b = 186},
		["Underdark - Inside"] = {id = 22, r = 132, g = 132, b = 132},
		["Underdark - Water (Swim)"] = {id = 23, r = 70, g = 139, b = 175},
		["Underdark - Water (No Swim)"] = {id = 24, r = 34, g = 68, b = 86},
		["Underdark - In Flight"] = {id = 25, r = 158, g = 178, b = 188},
		["Lava"] 						= {id = 26, r = 255, g = 119, b = 0},
		["Dirt Road North-South"] = {id = 27, r = 142, g = 85, b = 0},
		["Dirt Road East-West"] = {id = 28, r = 142, g = 85, b = 0},
		["Dirt Road Intersection"] = {id = 29, r = 142, g = 85, b = 0},
		["Cave"] 						= {id = 30, r = 80, g = 80, b = 80},
		["Jungle"] 					= {id = 31, r = 21, g = 132, b = 101},
		["Tundra"] 					= {id = 32, r = 224, g = 224, b = 224},
		["Taiga"] 					= {id = 33, r = 103, g = 137, b = 104},
		["Beach"] 					= {id = 34, r = 239, g = 235, b = 0},
}

-- list of possible movement directions and appropriate coordinate changes
local move_vectors = {
    north = {0,1,0}, south = {0,-1,0}, east = {1,0,0}, west = {-1,0,0},
    northwest = {-1,1,0}, northeast = {1,1,0}, southwest = {-1,-1,0}, southeast = {1,-1,0},
    up = {0,0,1}, down = {0,0,-1}
}

-- used to convert short dirs for full dirs
local exits = {
    n = "north", s = "south", w = "west", e = "east",
    nw = "northwest", ne = "northeast", sw = "southwest", se = "southeast",
    u = "up", d = "down"
}

local exitmap = {
    north = 1,      northeast = 2,      northwest = 3,      east = 4,
    west = 5,       south = 6,          southeast = 7,      southwest = 8,
    up = 9,         down = 10,          ["in"] = 11,        out = 12,
    [1] = "north",  [2] = "northeast",  [3] = "northwest",  [4] = "east",
    [5] = "west",   [6] = "south",      [7] = "southeast",  [8] = "southwest",
    [9] = "up",     [10] = "down",      [11] = "in",        [12] = "out",
}
	
for k, v in pairs(exitmap) do
	
end	

local function make_room()
    local info = map.room_info
    local coords = {0,0,0}
    addRoom(info.VNUM)
    local areas = getAreaTable()
    local areaID = areas[info.AREA]
    if not areaID then
        areaID = addAreaName(info.AREA)
    else
        coords = {getRoomCoordinates(map.prev_info.VNUM)}								
        local shift = {0,0,0}
        for k,v in pairs(map.prev_info.EXITS) do
            if v == info.VNUM and move_vectors[k] then
                shift = move_vectors[k]								
                break
            end
        end
				
        for n = 1,3 do
            coords[n] = coords[n] + shift[n]
        end
											
        -- map stretching
        local overlap = getRoomsByPosition(areaID,coords[1],coords[2],coords[3])				
				
        if not table.is_empty(overlap) then
            local rooms = getAreaRooms(areaID)
            local rcoords		
            for _,id in ipairs(rooms) do
                local x, y, z = getRoomCoordinates(id)
								rcoords = {x, y, z}
                for n = 1,3 do
                    if shift[n] ~= 0 and (rcoords[n] - coords[n]) * shift[n] &gt;= 0 then
                        rcoords[n] = rcoords[n] + shift[n]
                    end
                end
                setRoomCoordinates(id,rcoords[1],rcoords[2],rcoords[3])
            end
        end
    end
		setRoomArea(info.VNUM, areaID)
    setRoomCoordinates(info.VNUM, coords[1], coords[2], coords[3])
    if terrain_types[info.TERRAIN] then
        setRoomEnv(info.VNUM, terrain_types[info.TERRAIN].id + 16)
    end
		if map.prev_info then -- Check if you moved into here from another room.
			local prev_exits = getRoomExits(map.prev_info.VNUM)
			for dir, id in pairs(map.prev_info.EXITS) do
				if prev_exits[dir] == nil then
			    if not setExit(map.prev_info.VNUM, id, exitmap[dir]) then       
            setExitStub(map.prev_info.VNUM, exitmap[dir], true)
          end
				end
			end
		end
    for dir, id in pairs(info.EXITS) do
        -- need to see how special exits are represented to handle those properly here				
        if not setExit(info.VNUM, id, exitmap[dir]) then       
            setExitStub(info.VNUM, exitmap[dir], true)
        end
    end
end

local function shift_room(dir)
    local ID = map.room_info.VNUM
    local x,y,z = getRoomCoordinates(ID)
    local x1,y1,z1 = move_vectors[dir]
    x = x + x1
    y = y + y1
    z = z + z1
    setRoomCoordinates(ID,x,y,z)
    updateMap()
end

local function handle_move()
	if map.enabled == true and map.room_info.ENVIRONMENT ~= "Wilderness" then
    if not getRoomName(map.room_info.VNUM) then
        make_room()
    else		
			if terrain_types[map.room_info.TERRAIN] then
        setRoomEnv(map.room_info.VNUM, terrain_types[map.room_info.TERRAIN].id + 16)
    	end 	  
      local stubs = getExitStubs1(map.room_info.VNUM)				
			if stubs then		
       	for _, n in ipairs(stubs) do
          local dir = exitmap[n]				
	        local id = map.room_info.EXITS[dir]
	        -- need to see how special exits are represented to handle those properly here
	        if getRoomName(id) then	        
					  setExit(map.room_info.VNUM, id, exitmap[dir])
	        end
  	    end
			end
    end
	end
  centerview(map.room_info.VNUM)
end

local function make_aliases()

-- Aliases
    -- Let the user shift a room around via command line
    table.insert(map.aliases,tempAlias([[^shift (\w+)$]],[[raiseEvent("shiftRoom",matches[2])]]))
		table.insert(map.aliases,tempAlias([[^mc on$]],[[raiseEvent("startMapping")]]))
		table.insert(map.aliases,tempAlias([[^mc off$]],[[raiseEvent("stopMapping")]]))
		
    map.aliases = map.aliases or {}
    local id
    local tbl = {
        ["Start Mapping Alias"] = {[[^start mapping$]], [[map.start_mapping()]]},
        ["Stop Mapping Alias"] = {[[^stop mapping$]], [[map.stop_mapping()]]},
				["Shift Room Alias"] = {[[^shift (\w+)$]],[[raiseEvent("shiftRoom",matches[2])]]},				
        
				--["Save Map Alias"] = {[[^save map$]], [[saveMap(getMudletHomeDir() .. "/map.dat")]]},
        --["Load Map Alias"] = {[[^load map(?: (local))?$]], [[map.load_map(matches[2])]]},
        --["Export Map Area Alias"] = {[[^export area (.*)]],[[map.export_area(matches[2])]]},
        --["Import Map Area Alias"] = {[[^import area (.*)]],[[map.import_area(matches[2])]]},

        --["Set Room Area Alias"] = {[[^set area (.*)$]], [[map.set_area(matches[2])]]},
        --["Set Map Mode Alias"] = {[[^map mode (\w+)$]],[[map.set_mode(matches[2])]]},
        
        --["Merge Rooms Alias"] = {[[^merge rooms$]], [[map.merge_rooms()]]},
        --["Add Door Alias"] = {[[^add door (\w+)(?: (none|open|closed|locked)(?: (yes|no))?)?$]],[[map.set_door(matches[2],matches[3],matches[4])]]},
        --["Add Portal Alias"] = {[[^add portal (.*)$]],[[map.set_portal(matches[2])]]},
        --["Set Room Exit Alias"] = {[[^set exit (.+) (\d+)]],[[map.set_exit(matches[2],matches[3])]]},
        --["Clear Moves Alias"] = {[[^clear moves$]], [[map.clear_moves()]]},

        --["Find Me Alias"] = {[[^find me$]], [[map.find_me()]]},
        --["Find Path Alias"] = {[[find path ([^;]+)(?:\s*;\s*(.+))?]],[[map.find_path(matches[2],matches[3])]]},
        --["Set Recall Alias"] = {[[^set recall$]],[[map.set_recall()]]},
        --["Set Character Alias"] = {[[^set character (.*)$]],[[map.character = matches[2]]},
    }
    for k,v in pairs(tbl) do
        if map.aliases[k] and exists(map.aliases[k],"alias") ~= 0 then
            killAlias(map.aliases[k])
        end
        id = tempAlias(v[1],v[2])
        map.aliases[k] = id
    end
end

--function map.set_door(dir,status,one_way)
--    -- adds a door on a given exit
--    if map.enabled then
--        if not map.room_info then error("Make Door: No room found.") end
--        dir = exitmap[dir] or dir
--        if not stubmap[dir] then error("Make Door: Invalid direction.") end
--        status = (status ~= "" and status) or "closed"
--        one_way = (one_way ~= "" and one_way) or "no"
--        if not table.contains({"yes","no"},one_way) then error("Make Door: Invalid one-way status, must be yes or no.") end

--        local exits = getRoomExits(currentRoom)
--        local target_room = exits[dir]
--        if target_room then
--            exits = getRoomExits(target_room)
--        end
--        if one_way == "no" and (target_room and exits[reverse_dirs[dir]] == currentRoom) then
--            add_door(target_room,reverse_dirs[dir],status)
--        end
--        add_door(currentRoom,dir,status)
--				print("Door added.")
--    end
--end

function map.load_map(use_local)
    local path = getMudletHomeDir() .. "/map.dat"		
    if use_local then
        loadMap(path)
        print("Map reloaded from local copy.")
    else
        local address = 'http://www.luminarimud.com/download/map.dat'
        downloading = true
        downloadFile(path,address)
        print("Downloading Map File.")
    end
end

function map.adjustMinimapFontSize()
  local w = map.minimap.get_width()
  local h = map.minimap.get_height()
  local font_size = 8
  repeat
    font_size = font_size + 1
    local width, height = calcFontSize(font_size)
    width = width * map.minimap_width
    height = height * map.minimap_height
  until (w &lt; width) or (h &lt; height)
	map.minimap_font_size = font_size - 1
  setMiniConsoleFontSize("map.minimap", map.minimap_font_size)
end

function map.adjustAsciimapFontSize()
  local w = map.minimap.get_width()
  local h = map.minimap.get_height()
  local font_size = 8
  repeat
    font_size = font_size + 1
    local width, height = calcFontSize(font_size)
    width = width * 20
    height = height * 11
  until (w &lt; width) or (h &lt; height)
	map.minimap_font_size = font_size - 1
  setMiniConsoleFontSize("map.minimap", map.minimap_font_size)
end

function map.calcMinimapPadding()
	local width = calcFontSize(map.minimap_font_size)
	local characters = map.minimap.get_width() / width
	return (characters - map.minimap_width) / 4
end

function map.calcAsciimapPadding()
	local width = calcFontSize(map.minimap_font_size)
	local characters = map.minimap.get_width() / width
	return (characters - 19) / 4
end

local function config()        
    -- setting terrain colors
    for k,v in pairs(terrain_types) do
        setCustomEnvColor(v.id + 16, v.r, v.g, v.b, 255)
    end
    -- making mapper window
    local info = defaults.mapper
		map.container = Geyser.Container:new({name = "map.container", x = info.x, y = info.y, width = info.width, height = info.height})
		map.minimapcontainer = Geyser.Container:new({name = "map.minimapcontainer", x = info.x, y = info.y, width = info.width, height = info.height})
		--map.minimapbackground = Geyser.Label:new({
--			name="map.minimapbackground",
--			x=0, y=0,
--			width="100%", height="100%",
--	  }, map.minimapcontainer)
--		map.minimapbackground:setColor(0,0,0)
		
		map.minimap = Geyser.MiniConsole:new({
  		name="map.minimap",
  		x=0, y= 0,
  		width="100%", height="100%",
		}, map.minimapcontainer)
		
		map.minimap:setColor("black")
		map.minimapcontainer:hide()
    
		map.mapwindow = Geyser.Mapper:new({name = "map.mapwindow", x = 0, y = 0, width = "100%", height = "100%"}, map.container)
		
		map.adjustMinimapFontSize()
		make_aliases()		
		
		map.get_default_map()
end

function map.get_default_map()
	-- If the user has no map, download the default map from the server!
	local areas = getAreaTable()
	local path = getMudletHomeDir() .. "/map.dat"
	 
	
	if (areas["Mosswood"] == nil) then
		local address = 'http://www.luminarimud.com/download/map.dat'
    downloading = true
    downloadFile(path,address)
    print("Downloading Map File.")
	end
end

function map.start_mapping()
  map.enabled=true
	print("Mapping enabled.")
end

function map.stop_mapping()
	map.enabled=false
	print("Mapping disabled.")
end

function map.eventHandler(event,...)     
    if event == "msdp.ROOM" then
        map.prev_info = map.room_info
        map.room_info = table.update({},msdp.ROOM)
				-- Check if we have moved between regular and wilderness areas

				if map.prev_info.ENVIRONMENT == "Wilderness" and map.room_info.ENVIRONMENT == "Room" then
					 -- re-enable the mapper!
					 map.minimapcontainer:hide()
					 map.container:show()					 
				elseif map.prev_info.ENVIRONMENT == "Room" and map.room_info.ENVIRONMENT == "Wilderness" then
					 -- disable the mapper!
					 map.container:hide()		
					 map.minimapcontainer:show()
				end
        handle_move()
    elseif event == "shiftRoom" then
        local dir = exits[arg[1]] or arg[1]
        if not table.contains(exits, dir) then
            echo("Error: Invalid direction '" .. dir .. "'.")
        else
            shift_room(dir)
        end
		elseif event == "sysDownloadDone" and downloading then
        loadMap(getMudletHomeDir() .. "/map.dat")
        downloading = false
        print("Map File Loaded.")				
    elseif event == "sysConnectionEvent" then
        config()			 
		end
		
end

function map.onProtocolEnabled(_, protocol)
  if protocol == "MSDP" then
    print("MSDP enabled!")
		sendMSDP("REPORT", "ROOM")
		config()	
  end
end

registerAnonymousEventHandler("msdp.ROOM","map.eventHandler")
registerAnonymousEventHandler("shiftRoom","map.eventHandler")
registerAnonymousEventHandler("sysConnectionEvent", "map.eventHandler")
registerAnonymousEventHandler("sysProtocolEnabled", "map.onProtocolEnabled")
registerAnonymousEventHandler("sysDownloadDone", "map.eventHandler")</script>
					<eventHandlerList />
				</Script>
			</ScriptGroup>
			<ScriptGroup isActive="yes" isFolder="yes">
				<name>GUI</name>
				<packageName>Template</packageName>
				<script></script>
				<eventHandlerList />
				<ScriptGroup isActive="yes" isFolder="yes">
					<name>CSSman</name>
					<packageName>CSSman</packageName>
					<script></script>
					<eventHandlerList />
					<Script isActive="yes" isFolder="no">
						<name>CSSMan</name>
						<packageName></packageName>
						<script>-- CSSMan by Vadi. Public domain.

CSSMan = {}
CSSMan.__index = CSSMan

function CSSMan.new(stylesheet)
  local obj  = { stylesheet = {} }
  setmetatable(obj,CSSMan)
  local trim = string.trim

  assert(type(stylesheet) == "string", "CSSMan.new: no stylesheet provided. A possible error is that you might have used CSSMan.new, not CSSMan:new")

  for line in stylesheet:gmatch("[^\r\n]+") do
    local attribute, value = line:match("^(.-):(.-);$")
    if attribute and value then
      attribute, value = trim(attribute), trim(value)
      obj.stylesheet[attribute] = value
    end
  end

  return obj
end

function CSSMan:set(key, value)
  self.stylesheet[key] = value
end

function CSSMan:get(key)
  return self.stylesheet[key]
end

function CSSMan:getCSS(key)
  local lines, concat = {}, table.concat
  for k,v in pairs(self.stylesheet) do lines[#lines+1] = concat({k,": ", v, ";"}) end
  return concat(lines, "\n")
end

function CSSMan:gettable()
  return self.stylesheet
end

function CSSMan:settable(tbl)
  assert(type(tbl) == "table", "CSSMan:settable: table expected, got "..type(tbl))

  self.stylesheet = tbl
end</script>
						<eventHandlerList />
					</Script>
				</ScriptGroup>
				<ScriptGroup isActive="yes" isFolder="yes">
					<name>GUI</name>
					<packageName></packageName>
					<script>GUI = GUI or {}
</script>
					<eventHandlerList />
					<Script isActive="yes" isFolder="no">
						<name>Toggles</name>
						<packageName></packageName>
						<script>--Toggle the gagging of chat from main buffer.
function GUI.gagChatToggle()
  if GUI.toggles.gagChat == false then
	  GUI.toggles.gagChat = true
		cecho("\n&lt;white&gt;Now gagging chat from main buffer.")
	elseif GUI.toggles.gagChat == true then
	  GUI.toggles.gagChat = false
		cecho("\n&lt;white&gt;No longer gagging chat from main buffer.")
	else
	  GUI.toggles = {
		       gagChat = true,
	  includeInGroup = true,
		}
		table.save(getMudletHomeDir().."/GUI.toggles.lua", GUI.toggles)
	end
end

--Toggles whether or not you are shown in the group tab.
function GUI.showSelfToggle()
  if GUI.toggles.includeInGroup == false then
	  GUI.toggles.includeInGroup = true
		GUI.updateGroup()
		cecho("\n&lt;white&gt;Now showing self in group.")
	elseif GUI.toggles.includeInGroup == true then
	  GUI.toggles.includeInGroup = false
		GUI.updateGroup()
		cecho("\n&lt;white&gt;No longer showing self in group.")
	else
	  GUI.toggles = {
		       gagChat = true,
	  includeInGroup = true,
		}
		table.save(getMudletHomeDir().."/GUI.toggles.lua", GUI.toggles)
		cecho("\n&lt;white&gt;Now showing self in group.")
	end
end


function GUI.loadToggles()
--Check for saved .lua. Create if not.
  GUI.toggles = {}
  if not io.exists(getMudletHomeDir().."/GUI.toggles.lua") then
	  GUI.toggles = {
		       gagChat = true,
	  includeInGroup = true,
		}
		table.save(getMudletHomeDir().."/GUI.toggles.lua", GUI.toggles)
	else
		table.load(getMudletHomeDir().."/GUI.toggles.lua", GUI.toggles)
  end
end

--Save Toggle Preferences
function GUI.saveToggles()
  table.save(getMudletHomeDir().."/GUI.toggles.lua", GUI.toggles)
end

registerAnonymousEventHandler("sysLoadEvent", "GUI.loadToggles")
registerAnonymousEventHandler("sysExitEvent", "GUI.saveToggles")</script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Create Background</name>
						<packageName></packageName>
						<script>function GUI.init_background()
  GUI.BackgroundCSS = CSSMan.new([[
  background-color: rgb(20,0,20);
]])
  -- Left
  GUI.Left = Geyser.Label:new({name = "GUI.Left", x = 0, y = 0, width = "25%", height = "100%"})
  GUI.Left:setStyleSheet(GUI.BackgroundCSS:getCSS())
  --Right
	GUI.Right =
    Geyser.Label:new({name = "GUI.Right", x = "-25%", y = "-50%", width = "25%", height = "50%"})
  GUI.Right:setStyleSheet(GUI.BackgroundCSS:getCSS())
	--Top
	--[[
	GUI.Top = Geyser.Label:new({name = "GUI.Top", x = "25%", y = 0, width = "50%", height = "25%"})
  GUI.Top:setStyleSheet(GUI.BackgroundCSS:getCSS())
	]]
	--Bottom
	GUI.Bottom = Geyser.Label:new({name = "GUI.Bottom", x = "25%", y = "-25%", width = "50%", height = "25%"})
  GUI.Bottom:setStyleSheet(GUI.BackgroundCSS:getCSS())
end</script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Set Borders</name>
						<packageName></packageName>
						<script>function GUI.set_borders()
  local w, h = getMainWindowSize()
  setBorderLeft(w / 4)
  setBorderTop(0)
  setBorderBottom(h / 4)
  setBorderRight(w / 4)
end</script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Boxes</name>
						<packageName></packageName>
						<script>function GUI.init_boxes()

  GUI.BoxCSS =
    CSSMan.new(
      [[    
	background-image: url(]] ..
      getMudletHomeDir():gsub("\\", "/") ..
      [[/LuminariGUI/images/ui_texture.png);
			font-family: Tahoma, Geneva, sans-serif;
]]
    )
  --GUI.Box1 = Geyser.Label:new({
  --  name = "GUI.Box1",
  --  x = 0, y = 0,
  --  width = "100%",
  --  height = "50%",
  --},GUI.Right)
  --GUI.Box1:setStyleSheet(GUI.BoxCSS:getCSS())
  --GUI.Box1:echo("&lt;center&gt;GUI.Box1")
	
	--Box2
  GUI.Box2 =
    Geyser.Label:new({name = "GUI.Box2", x = 0, y = 0, width = "100%", height = "100%", color = "black"}, GUI.Bottom)
  GUI.Box2:setStyleSheet(GUI.BoxCSS:getCSS())
  GUI.Box2:echo("&lt;center&gt;GUI.Box2")
  GUI.chatContainer =
    Geyser.Label:new(
      {
        name = "GUI.chatContainer",
        x = 8,
        y = "15%",
        width = GUI.Box2:get_width() - 14,
				--Height subtraction is found by adding y to button height.
        height = "70%",
      },
      GUI.Box2
    )

  --Box3
  GUI.Box3 =
    Geyser.Label:new(
      {name = "GUI.Box3", x = "0%", y = "0%", width = "100%", height = "15%"}, GUI.Right
    )
  GUI.Box3CSS = CSSMan.new(GUI.BoxCSS:getCSS())
  GUI.Box3CSS:set("border-width", "0px")
  GUI.Box3:setStyleSheet(GUI.Box3CSS:getCSS())
  GUI.Box3:echo("&lt;center&gt;GUI.Box3")

  --Box4
  GUI.Box4 =
    Geyser.Label:new(
      {name = "GUI.Box4", x = "0%", y = "0%", width = "100%", height = "75%"}, GUI.Left
    )
  GUI.Box4CSS = CSSMan.new(GUI.BoxCSS:getCSS())
  GUI.Box4CSS:set("border-width", "0px")
  GUI.Box4:setStyleSheet(GUI.Box4CSS:getCSS())
  GUI.Box4:echo("&lt;center&gt;GUI.Box4")

  --Box5
  GUI.Box5 =
    Geyser.Label:new(
      {name = "GUI.Box5", x = "0%", y = "15%", width = "100%", height = "50%"}, GUI.Right
    )
  GUI.Box5CSS = CSSMan.new(GUI.BoxCSS:getCSS())
  GUI.Box5CSS:set("border-width", "0px")
  GUI.Box5:setStyleSheet(GUI.Box5CSS:getCSS())
  GUI.Box5:echo("&lt;center&gt;Legend When Toggled/Room Info")
 
 --Commenting out Box 6 echo until skill Icons are finished. 
	GUI.Box6 =
    Geyser.Label:new(
      {name = "GUI.Box6", x = "0%", y = "65%", width = "100%", height = "35%"}, GUI.Right
    )
  GUI.Box6CSS = CSSMan.new(GUI.BoxCSS:getCSS())
  GUI.Box6CSS:set("border-width", "0px")
  GUI.Box6:setStyleSheet(GUI.Box6CSS:getCSS())
  --GUI.Box6:echo("&lt;center&gt;Skill Icons")

  --Box7
  GUI.Box7 =
    Geyser.Label:new(
      {name = "GUI.Box7", x = "0%", y = "75%", width = "100%", height = "25%"}, GUI.Left
    )
  GUI.Box7:setStyleSheet(GUI.BoxCSS:getCSS())
end</script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Gauges</name>
						<packageName></packageName>
						<script>function GUI.init_gauges()
  GUI.Footer =
    Geyser.HBox:new(
      {name = "GUI.Footer", x = 0, y = 10, width = "100%", height = "75%"}, GUI.Bottom
    )
  GUI.Status =
    Geyser.HBox:new(
      {name = "GUI.Status", x = 12, y = 12, width = "100%", height = "100%", margin = 11}, GUI.Box7
    )
  GUI.ActionIconsBox =
    Geyser.VBox:new(
      {
        name = "GUI.ActionIconsBox",
        x = 0,
        y = 0,
        height = "100%",
        width = 32,
        h_policy = Geyser.Fixed,
        v_policy = Geyser.Fixed,
      },
      GUI.Status
    )
  GUI.GaugeBar =
    Geyser.VBox:new(
      {
        name = "GUI.GaugeBar",
        x = 0,
        y = 0,
        height = "100%",
        width = GUI.Status.get_width() - 20 - 32,
        h_policy = Geyser.Fixed,
        v_policy = Geyser.Fixed,
      },
      GUI.Status
    )
  --GUI.LeftColumn = Geyser.VBox:new({
  --  name = "GUI.LeftColumn",
  --},GUI.Footer)
  --GUI.RightColumn = Geyser.VBox:new({
  --  name = "GUI.RightColumn",
  --},GUI.Footer)
  GUI.GaugeBackCSS =
    CSSMan.new(
      [[
  background-color: rgba(0,0,0,0);
  border-style: solid;
  border-color: white;
  border-width: 1px;
  border-radius: 5px;
  margin: 5px;
	width: 100%;
	font-family: BitstreamVeraSans;
]]
    )
  GUI.GaugeFrontCSS =
    CSSMan.new(
      [[
  background-color: rgba(0,0,0,0);
  border-style: solid;
  border-color: white;
  border-width: 1px;
  border-radius: 7px;
  margin: 5px;
	width: 100%;
	font-family: BitstreamVeraSans;
]]
    )
  GUI.Health =
    Geyser.Gauge:new({name = "GUI.Health", height = 32, v_policy = Geyser.Fixed}, GUI.GaugeBar)
  GUI.GaugeFrontCSS:set("background-color", "green")
  GUI.GaugeBackCSS:set("background-color", "dark_green")
  GUI.Health.front:setStyleSheet(GUI.GaugeFrontCSS:getCSS())
  GUI.Health.back:setStyleSheet(GUI.GaugeBackCSS:getCSS())
  GUI.Health:setValue(math.random(100), 100)
  GUI.Health.front:echo("GUI.Health")
  GUI.Moves =
    Geyser.Gauge:new({name = "GUI.Moves", height = 32, v_policy = Geyser.Fixed}, GUI.GaugeBar)
  GUI.GaugeFrontCSS:set("background-color", "purple")
  GUI.GaugeBackCSS:set("background-color", "dark_purple")
  GUI.Moves.front:setStyleSheet(GUI.GaugeFrontCSS:getCSS())
  GUI.Moves.back:setStyleSheet(GUI.GaugeBackCSS:getCSS())
  GUI.Moves:setValue(math.random(100), 100)
  GUI.Moves.front:echo("GUI.Moves")
  GUI.Experience =
    Geyser.Gauge:new({name = "GUI.Experience", height = 32, v_policy = Geyser.Fixed}, GUI.GaugeBar)
  GUI.GaugeFrontCSS:set("background-color", "yellow")
  GUI.GaugeBackCSS:set("background-color", "dark_yellow")
  GUI.Experience.front:setStyleSheet(GUI.GaugeFrontCSS:getCSS())
  GUI.Experience.back:setStyleSheet(GUI.GaugeBackCSS:getCSS())
  GUI.Experience:setValue(math.random(100), 100)
  GUI.Experience.front:echo([[&lt;span style = "color: black"&gt;GUI.Experience&lt;/span&gt;]])
  GUI.Enemy =
    Geyser.Gauge:new({name = "GUI.Enemy", height = 32, v_policy = Geyser.Fixed}, GUI.GaugeBar)
  GUI.GaugeFrontCSS:set("background-color", "brown")
  GUI.GaugeBackCSS:set("background-color", "dark_brown")
  GUI.Enemy.front:setStyleSheet(GUI.GaugeFrontCSS:getCSS())
  GUI.Enemy.back:setStyleSheet(GUI.GaugeBackCSS:getCSS())
  GUI.Enemy:setValue(math.random(100), 100)
  GUI.Enemy.front:echo([[GUI.Enemy]])
  GUI.AffectedByIconsBox =
    Geyser.HBox:new(
      {
        name = "GUI.AffectedByIconsBox",
        x = 0,
        y = 0,
        height = 48,
        width = "100%",
        h_policy = Geyser.Fixed,
        v_policy = Geyser.Fixed,
      },
      GUI.GaugeBar
    )
end

function GUI.init_action_icons()
  GUI.ActionIconCSS = CSSMan.new([[
	margin: 0px;
]])
  GUI.StandardActionIcon =
    Geyser.Label:new(
      {
        name = "GUI.StandardActionIcon",
        width = 32,
        height = 32,
        h_policy = Geyser.Fixed,
        v_policy = Geyser.Fixed,
      },
      GUI.ActionIconsBox
    )
  GUI.ActionIconCSS:set(
    "border-image",
    [[url("]] ..
    getMudletHomeDir():gsub("\\", "/") ..
    [[/LuminariGUI/images/action-standard.png");]]
  )
  GUI.StandardActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
  GUI.MoveActionIcon =
    Geyser.Label:new(
      {
        name = "GUI.MoveActionIcon",
        width = 32,
        height = 32,
        h_policy = Geyser.Fixed,
        v_policy = Geyser.Fixed,
      },
      GUI.ActionIconsBox
    )
  GUI.ActionIconCSS:set(
    "border-image",
    [[url("]] .. getMudletHomeDir():gsub("\\", "/") .. [[/LuminariGUI/images/action-move.png");]]
  )
  GUI.MoveActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
  GUI.SwiftActionIcon =
    Geyser.Label:new(
      {
        name = "GUI.SwiftActionIcon",
        width = 32,
        height = 32,
        h_policy = Geyser.Fixed,
        v_policy = Geyser.Fixed,
      },
      GUI.ActionIconsBox
    )
  GUI.ActionIconCSS:set(
    "border-image",
    [[url("]] .. getMudletHomeDir():gsub("\\", "/") .. [[/LuminariGUI/images/action-swift.png");]]
  )
  GUI.SwiftActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
end

--GUI.Endurance = Geyser.Gauge:new({
--  name = "GUI.Endurance",
--},GUI.RightColumn)
--GUI.Endurance.back:setStyleSheet(GUI.GaugeBackCSS:getCSS())
--GUI.GaugeFrontCSS:set("background-color","yellow")
--GUI.Endurance.front:setStyleSheet(GUI.GaugeFrontCSS:getCSS())
--GUI.Endurance:setValue(math.random(100),100)
--GUI.Endurance.front:echo("GUI.Endurance")
--GUI.Endurance.front:echo([[&lt;span style = "color: black"&gt;GUI.Endurance&lt;/span&gt;]])
--GUI.Willpower = Geyser.Gauge:new({
--  name = "GUI.Willpower",
--},GUI.RightColumn)
--GUI.Willpower.back:setStyleSheet(GUI.GaugeBackCSS:getCSS())
--GUI.GaugeFrontCSS:set("background-color","purple")
--GUI.Willpower.front:setStyleSheet(GUI.GaugeFrontCSS:getCSS())
--GUI.Willpower:setValue(math.random(100),100)
--GUI.Willpower.front:echo("GUI.Willpower")</script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Cast Console</name>
						<packageName></packageName>
						<script>function GUI.init_castConsole()
  GUI.castConsole =
    Geyser.MiniConsole:new(
      {
        name = "GUI.castConsole",
        x = 8,
        y = "5%",
        width = GUI.Box2:get_width() - 15,
				--Height subtraction is found by adding y to button height.
        height = "10%",
				color = "black"
      },
      GUI.Box2
    )
	setMiniConsoleFontSize("GUI.castConsole", getFontSize("main"))
end


function GUI.castConsole_startCast(spellName, spellLength)
  GUI.currentlyCasting = spellName
	GUI.castingNow = true
  clearUserWindow("GUI.castConsole")
  GUI.castConsole:cecho("\n&lt;white&gt;Spell: &lt;yellow&gt;"..spellName:title().." &lt;white&gt;- &lt;cyan&gt;"..spellLength)
	if GUI.castConsoleTimer then
	  killTimer(GUI.castConsoleTimer)
	end
end

function GUI.castConsole_completeCast()
  GUI.castConsole:cecho("\n&lt;white&gt;Spell: &lt;yellow&gt;"..GUI.currentlyCasting:title().." &lt;white&gt;- &lt;green&gt;Cast")
	GUI.castConsoleTimer = tempTimer(10, [[clearUserWindow("GUI.castConsole")]])
end

function GUI.castConsole_abortedCast()
  GUI.castConsole:cecho("\n&lt;white&gt;Spell: &lt;yellow&gt;"..GUI.currentlyCasting:title().." &lt;white&gt;- &lt;red&gt;Aborted")
	GUI.castingNow = false
	GUI.castConsoleTimer = tempTimer(10, [[clearUserWindow("GUI.castConsole")]])
end


function GUI.castConsole_canceledCast()
  GUI.castConsole:cecho("\n&lt;white&gt;Spell: &lt;yellow&gt;"..GUI.currentlyCasting:title().." &lt;white&gt;- &lt;red&gt;Canceled")
	GUI.castingNow = false
	GUI.castConsoleTimer = tempTimer(10, [[clearUserWindow("GUI.castConsole")]])
end

</script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Header Icons</name>
						<packageName></packageName>
						<script>function GUI.init_header_icons()
  GUI.Header =
    Geyser.HBox:new({name = "GUI.Header", x = 0, y = 0, width = "100%", height = "100%"}, GUI.Top)
  GUI.IconCSS =
    CSSMan.new(
      [[
  background-color: rgba(0,0,0,100);
  border-style: solid;
  border-width: 1px;
  border-color: white;
  border-radius: 5px;
  margin: 5px;
  qproperty-wordWrap: true;
]]
    )
  --[[
for i=1,12 do
  GUI["Icon"..i] = Geyser.Label:new({
    name = "GUI.Icon"..i,
  },GUI.Header)
  GUI["Icon"..i]:setStyleSheet(GUI.IconCSS:getCSS())
  GUI["Icon"..i]:echo("&lt;center&gt;GUI. Icon"..i)
end
]]
end</script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>TabbedInfoWindow</name>
						<packageName></packageName>
						<script>GUI.tabbedInfoWindow =
  GUI.tabbedInfoWindow or
  {
    tabs = {"Player", "Affects", "Group"},
    color1 = "rgba(0,0,0,0)",
    color2 = "rgba(0,0,0,0)",
    width = "100%",
    height = "100%",
    current = "Player",
  }

function createFrame(fl)
  -- Set the frame around the inside of box4, then create a container inside to hold the tabbed window.
  ft = {}
  ft.upper_left = Geyser.Label:new({x = 0, y = 0, height = 133, width = 9}, fl)
  ft.upper_left:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub("\\", "/") ..
    [[/LuminariGUI/images/frame/frame_upperleft_9x133.png) 0 0 0 0 stretch stretch;
  ]]
  )
  ft.middle_left =
    Geyser.Label:new({x = 0, y = 133, height = fl:get_height() - 266, width = 9}, fl)
  ft.middle_left:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub("\\", "/") ..
    [[/LuminariGUI/images/frame/straight_repeater_left_9x133.png) 0 0 0 0 stretch repeat;
  ]]
  )
  ft.lower_left = Geyser.Label:new({x = 0, y = -133, height = 133, width = 9}, fl)
  ft.lower_left:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub("\\", "/") ..
    [[/LuminariGUI/images/frame/frame_lowerleft_9x133.png) 0 0 0 0 stretch stretch;
  ]]
  )
  ft.upper_right = Geyser.Label:new({x = -9, y = 0, height = 133, width = 9}, fl)
  ft.upper_right:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub("\\", "/") ..
    [[/LuminariGUI/images/frame/frame_upperright_9x133.png) 0 0 0 0 stretch stretch;
  ]]
  )
  ft.middle_right =
    Geyser.Label:new({x = -9, y = 133, height = fl:get_height() - 266, width = 9}, fl)
  ft.middle_right:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub("\\", "/") ..
    [[/LuminariGUI/images/frame/straight_repeater_right_9x133.png) 0 0 0 0 stretch repeat;
  ]]
  )
  ft.lower_right = Geyser.Label:new({x = -9, y = -133, height = 133, width = 9}, fl)
  ft.lower_right:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub("\\", "/") ..
    [[/LuminariGUI/images/frame/frame_lowerright_9x133.png) 0 0 0 0 stretch stretch;
  ]]
  )
  ft.left_top = Geyser.Label:new({x = 0, y = 0, height = 9, width = 133}, fl)
  ft.left_top:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub("\\", "/") ..
    [[/LuminariGUI/images/frame/frame_lefttop_133x9.png) 0 0 0 0 stretch stretch;
  ]]
  )
  ft.middle_top =
    Geyser.Label:new({x = 133, y = 0, height = 9, width = fl:get_width() - 266}, fl)
  ft.middle_top:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub("\\", "/") ..
    [[/LuminariGUI/images/frame/straight_repeater_top_133x9.png) 0 0 0 0 stretch repeat;
  ]]
  )
  ft.right_top = Geyser.Label:new({x = -133, y = 0, height = 9, width = 133}, fl)
  ft.right_top:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub("\\", "/") ..
    [[/LuminariGUI/images/frame/frame_righttop_133x9.png) 0 0 0 0 stretch stretch;
  ]]
  )
  ft.left_bottom = Geyser.Label:new({x = 0, y = -9, height = 9, width = 133}, fl)
  ft.left_bottom:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub("\\", "/") ..
    [[/LuminariGUI/images/frame/frame_leftbottom_133x9.png) 0 0 0 0 stretch stretch;
  ]]
  )
  ft.middle_bottom =
    Geyser.Label:new({x = 133, y = -9, height = 9, width = fl:get_width() - 266}, fl)
  ft.middle_bottom:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub("\\", "/") ..
    [[/LuminariGUI/images/frame/straight_repeater_bottom_133x9.png) 0 0 0 0 stretch repeat;
  ]]
  )
  ft.right_bottom = Geyser.Label:new({x = -133, y = -9, height = 9, width = 133}, fl)
  ft.right_bottom:setStyleSheet(
    [[
  border-image: url(]] ..
    getMudletHomeDir():gsub("\\", "/") ..
    [[/LuminariGUI/images/frame/frame_rightbottom_133x9.png) 0 0 0 0 stretch stretch;
  ]]
  )
end

function GUI.tabbedInfoWindow.click(tab)
  GUI.tabbedInfoWindow[GUI.tabbedInfoWindow.current]:hide()
  GUI.tabbedInfoWindow[GUI.tabbedInfoWindow.current .. "tab"]:echo(
    GUI.tabbedInfoWindow.current, "white", "c"
  )
  GUI.tabbedInfoWindow.current = tab
  GUI.tabbedInfoWindow[GUI.tabbedInfoWindow.current]:show()
  GUI.tabbedInfoWindow[tab .. "tab"]:echo(tab, "yellow", "c")
end

function GUI.tabbedInfoWindow.init()
  GUI.tabbedInfoWindowTabCSS =
    CSSMan.new(
      [[font-family: Tahoma, Geneva, sans-serif; 
			border-image: url(]] ..
      getMudletHomeDir():gsub("\\", "/") ..
      [[/LuminariGUI/images/buttons/button.png) 0 0 0 0 stretch stretch;]]
    )
  GUI.tabbedInfoWindow.container =
    Geyser.Container:new(
      {
        name = "GUI.tabbedInfoWindow.back",
        x = 9,
        y = 9,
        width = GUI.Box4:get_width() - 18,
        height = GUI.Box4:get_height() - 18,
      },
      GUI.Box4
    )
  GUI.tabbedInfoWindow.header =
    Geyser.HBox:new(
      {name = "GUI.tabbedInfoWindow.header", x = 0, y = 0, width = "100%", height = "5%"},
      GUI.tabbedInfoWindow.container
    )
  GUI.tabbedInfoWindow.footer =
    Geyser.Label:new(
      {name = "GUI.tabbedInfoWindow.footer", x = 0, y = "5%", width = "100%", height = "95%"},
      GUI.tabbedInfoWindow.container
    )
  GUI.tabbedInfoWindow.footer:setStyleSheet([[font-family: Tahoma, Geneva, sans-serif;
]])
  GUI.tabbedInfoWindow.center =
    Geyser.Label:new(
      {name = "GUI.tabbedInfoWindow.center", x = 0, y = 0, width = "100%", height = "100%"},
      GUI.tabbedInfoWindow.footer
    )
  GUI.tabbedInfoWindow.center:setStyleSheet(
    [[
  background-color: ]] ..
    GUI.tabbedInfoWindow.color2 ..
    [[
	font-family: Tahoma, Geneva, sans-serif;
	]]
  )
  for k, v in pairs(GUI.tabbedInfoWindow.tabs) do
    GUI.tabbedInfoWindow[v .. "tab"] =
      Geyser.Label:new({name = "GUI.tabbedInfoWindow." .. v .. "tab"}, GUI.tabbedInfoWindow.header)
    GUI.tabbedInfoWindow[v .. "tab"]:setStyleSheet(GUI.tabbedInfoWindowTabCSS:getCSS())
    GUI.tabbedInfoWindow[v .. "tab"]:echo("&lt;center&gt;" .. v)
    GUI.tabbedInfoWindow[v .. "tab"]:setClickCallback("GUI.tabbedInfoWindow.click", v)
    GUI.tabbedInfoWindow[v] =
      Geyser.Label:new(
        {name = "GUI.tabbedInfoWindow." .. v, x = 0, y = 0, width = "100%", height = "100%"},
        GUI.tabbedInfoWindow.footer
      )
    GUI.tabbedInfoWindow[v]:setStyleSheet(
      [[
    background-color: ]] ..
      GUI.tabbedInfoWindow.color1 ..
      [[;    
	  font-family: Tahoma, Geneva, sans-serif;
	]]
    )
    GUI.tabbedInfoWindow[v .. "center"] =
      Geyser.Label:new(
        {
          name = "GUI.tabbedInfoWindow." .. v .. "center",
          x = 0,
          y = 0,
          width = "100%",
          height = "100%",
        },
        GUI.tabbedInfoWindow[v]
      )
    GUI.tabbedInfoWindow[v .. "center"]:setStyleSheet(
      [[
    background-color: ]] ..
      GUI.tabbedInfoWindow.color2 ..
      [[;
		border-image: url(]] ..
      getMudletHomeDir():gsub("\\", "/") ..
      [[/LuminariGUI/images/ui_texture.png) 0 0 0 0 stretch stretch;
		font-family: Tahoma, Geneva, sans-serif;
  ]]
    )
    GUI.tabbedInfoWindow[v]:hide()
    GUI.tabbedInfoWindow.current = v
  end
  -- Init to player tab
  GUI.tabbedInfoWindow.click("Player")
end</script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Affects</name>
						<packageName></packageName>
						<script>-- Affects
-- -------
-- There are several types of affects in Luminari - Affected By (Status), Spell-Like Affects, Resistances and Damage Reduction
-- 'Affected By' also includes 'Modes' which change the character's behavior in some way, for example 'Flurry-of-Blows' or
-- 'Rapid-Shot'.  Modes are displayed in the box with the health bars, as this is important information even if you are not
-- Interested in the other types of affects.
--
-- Spell-like affects can create 'Affected By' status flags.
--
-- All other affects are displayed in the 'Affects' tab in the upper left.
--
-- Container: GUI.tabbedInfoWindow["Affectscenter"]
--

-- Initialize tables
GUI.AffectIcons = GUI.AffectIcons or {}
GUI.SLAffects = GUI.SLAffects or {}
GUI.SLAffects.Labels = GUI.SLAffects.Labels or {}
GUI.Affects = GUI.Affects or {}
GUI.Affects.Rows = GUI.Affects.Rows or {}
GUI.Affects.Modes = GUI.Affects.Modes or {}

function GUI.Affects.init()
  -- Initialize all the labels and such	
  GUI.Affects = {}
  GUI.Affects.Labels = {}
  GUI.Affects.Modes = {}
  GUI.Affects.Modes.Labels = {}
  GUI.Affects.Rows = {}
  -- CSS
  GUI.Affects.IconCSS = CSSMan.new([[
	  margin: 0px;	
  ]])
  GUI.Affects.Modes.IconCSS = CSSMan.new([[
	  margin: 0px;
  ]])  
  -- Calculate how many labels will fit into the container
  GUI.Affects.icon_width = 48
  GUI.Affects.icon_height = 48
  GUI.Affects.Modes.icon_width = 48
  GUI.Affects.Modes.icon_height = 48
  GUI.Affects.num_icons_row =
    (GUI.tabbedInfoWindow["Affectscenter"]:get_width()) / GUI.Affects.icon_width
  GUI.Affects.num_rows = 3 -- This is so we have room below for the other affect types.	
    --(GUI.tabbedInfoWindow["Affectscenter"]:get_height()) / GUI.Affects.icon_height
  
	-- Create the VBox
  GUI.Affects.container =
    Geyser.VBox:new(
      {
        name = "GUI.Affects.container",
        x = 0,
        y = 0,
        width = "100%",
        height = GUI.Affects.num_rows * GUI.Affects.icon_height,
        h_policy = Geyser.Fixed,
        v_policy = Geyser.Fixed,
      },
      GUI.tabbedInfoWindow["Affectscenter"]
    )
	-- Create the Affect Labels
  for i = 1, GUI.Affects.num_rows do
    --GUI.Affects.icon_width * GUI.Affects.num_icons_row,
    GUI.Affects.Rows[i] = {}
    GUI.Affects.Rows[i].Labels = {}
    GUI.Affects.Rows[i].container =
      Geyser.HBox:new(
        {
          name = "GUI.Affects.Row" .. i,
          x = 0,
          y = 0,
          height = GUI.Affects.icon_height,
          width = "100%",
          h_policy = Geyser.Fixed,
          v_policy = Geyser.Fixed,
        },
        GUI.Affects.container
      )
    for j = 1, GUI.Affects.num_icons_row do
      GUI.Affects.Rows[i].Labels[j] =
        Geyser.Label:new(
          {
            name = "GUI.Affects.Label" .. tostring(i) .. '_' .. tostring(j),
            width = GUI.Affects.icon_width,
            height = GUI.Affects.icon_height,
            h_policy = Geyser.Fixed,
            v_policy = Geyser.Fixed,
          },
          GUI.Affects.Rows[i].container
        )
      GUI.Affects.Rows[i].Labels[j]:setStyleSheet(GUI.Affects.IconCSS:getCSS())
      GUI.Affects.Rows[i].Labels[j]:setColor(0, 0, 0, 0)
    end
    GUI.Affects.current_row = 1
    GUI.Affects.current_column = 1
  end
  -- Initialize Modes - These will display along with the health bars.
  GUI.Affects.Modes.num_icons_row =
    GUI.AffectedByIconsBox:get_width() / GUI.Affects.Modes.icon_width
  GUI.Affects.Modes.ModeList =
    {
      ["Mode-RapidShot"] = true,
      ["Flurry-of-Blows"] = true,
      ["Sneaking"] = true,
      ["Hiding"] = true,
      ["Mode-PowerAttack"] = true,
      ["Mode-Expertise"] = true,
      ["Mode-Total-Defense"] = true,
      ["Spot-Mode"] = true,
      ["Listen-Mode"] = true,
      ["Mode-Spellbattle"] = true,
      ["Counterspell"] = true,
      ["Defensive-Casting"] = true,
      ["Charging"] = true,
      ["WildShape"] = true,
    }
  -- Create the Mode Labels
  for i = 1, GUI.Affects.Modes.num_icons_row do
    GUI.Affects.Modes.Labels[i] =
      Geyser.Label:new(
        {
          name = "GUI.AffectIcon" .. tostring(i),
          width = GUI.Affects.Modes.icon_width,
          height = GUI.Affects.Modes.icon_height,
          h_policy = Geyser.Fixed,
          v_policy = Geyser.Fixed,
        },
        GUI.AffectedByIconsBox
      )
    GUI.Affects.Modes.Labels[i]:setStyleSheet(GUI.Affects.Modes.IconCSS:getCSS())
  end
	GUI.tabbedInfoWindow["Affects"]:hide()
	
	-- Initialize the area for the Spell Like Affects - GUI.Affects.SLAffects
	GUI.SLAffects.row_height = 20
	-- Calculate how many rows we can have:
	GUI.SLAffects.num_rows = (GUI.tabbedInfoWindow["Affectscenter"]:get_height() - (GUI.Affects.icon_height*GUI.Affects.num_rows)) / GUI.SLAffects.row_height
	-- Set up the VBox
	GUI.SLAffects.container = 
	Geyser.VBox:new(
      {
        name = "GUI.SLAffects.container",
        x = 0,
        y = GUI.Affects.icon_height*GUI.Affects.num_rows,
        width = "100%",
        height = GUI.tabbedInfoWindow["Affectscenter"].get_height() - (GUI.Affects.icon_height*GUI.Affects.num_rows),        
        v_policy = Geyser.Fixed,
      },
      GUI.tabbedInfoWindow["Affectscenter"]
  )

	for i = 1, GUI.SLAffects.num_rows do
		GUI.SLAffects.Labels[i] = 
			Geyser.Label:new(
				{
					name = "GUI.SLAffect" .. tostring(i),
					width = "100%",
					height = GUI.SLAffects.row_height,
					h_policy = Geyser.Fixed,
				}, GUI.SLAffects.container
			)
		GUI.SLAffects.Labels[i]:setStyleSheet([[
        background-color: rgba(0,0,0,0%);
      ]])
    GUI.SLAffects.Labels[i]:hide()
	end 
end

function GUI.updateSLAffects()
	if msdp.AFFECTS and msdp.AFFECTS.SPELL_LIKE_AFFECTS and #msdp.AFFECTS.SPELL_LIKE_AFFECTS &gt; 0 then
		-- We have spell like affects to process!		
		for i = 1, GUI.SLAffects.num_rows do
			GUI.SLAffects.Labels[i]:echo("")
			GUI.SLAffects.Labels[i]:hide()
			if i &lt;= #msdp.AFFECTS.SPELL_LIKE_AFFECTS then
			  if (msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].LOCATION == "Damage-Reduction") then
					affect_string = string.format("&lt;pre&gt;[ %s ] %s %s (%s)&lt;/pre&gt;", msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].DURATION, msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].NAME, 
					msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].LOCATION, msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].TYPE)
				else
				  affect_string = string.format("&lt;pre&gt;[ %s ] %s %s to %s (%s)&lt;/pre&gt;", msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].DURATION, msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].NAME, 
					msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].MODIFIER, msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].LOCATION, msdp.AFFECTS.SPELL_LIKE_AFFECTS[i].TYPE)
				end
				GUI.SLAffects.Labels[i]:echo(affect_string)
				GUI.SLAffects.Labels[i]:show()
			end
		end		 
	end
end

function GUI.updateAffectIcons()
  if msdp.AFFECTS and msdp.AFFECTS.AFFECTED_BY and #msdp.AFFECTS.AFFECTED_BY &gt; 0 then
    -- We have a status to process!  Do MODES first.
    affected_by = msdp.AFFECTS.AFFECTED_BY
    for k, _ in ipairs(GUI.Affects.Modes.Labels) do
      GUI.Affects.Modes.Labels[k]:hide()
    end
    for k, _ in ipairs(GUI.Affects.Rows) do
      for l, _ in ipairs(GUI.Affects.Rows[k].Labels) do
        GUI.Affects.Rows[k].Labels[l]:hide()
      end
    end
    GUI.Affects.Modes.current_icon = 1
    GUI.Affects.current_row = 1
    GUI.Affects.current_column = 1
    for i = 1, #affected_by do
      -- Is this a Mode?
      if
        (
          (GUI.Affects.Modes.current_icon &lt;= GUI.Affects.Modes.num_icons_row) and
          (GUI.Affects.Modes.ModeList[affected_by[i].NAME] == true)
        )
      then
        -- Create the Icon!				
        GUI.Affects.Modes.IconCSS:set(
          "border-image",
          [[url("]] ..
          getMudletHomeDir():gsub("\\", "/") ..
          [[/LuminariGUI/images/affected_by/]] ..
          affected_by[i].NAME ..
          [[.png");]]
        )
        GUI.Affects.Modes.Labels[GUI.Affects.Modes.current_icon]:setStyleSheet(
          GUI.Affects.Modes.IconCSS:getCSS()
        )
        GUI.Affects.Modes.Labels[GUI.Affects.Modes.current_icon]:show()
        GUI.Affects.Modes.current_icon = GUI.Affects.Modes.current_icon + 1
      else
        -- This is just an Affected By status flag
        if
          (
            (GUI.Affects.current_column &lt;= GUI.Affects.num_icons_row) and
            (GUI.Affects.current_row &lt;= GUI.Affects.num_rows)
          )
        then
          -- Create the Icon!				
          GUI.Affects.IconCSS:set(
            "border-image",
            [[url("]] ..
            getMudletHomeDir():gsub("\\", "/") ..
            [[/LuminariGUI/images/affected_by/]] ..
            affected_by[i].NAME ..
            [[.png");]]
          )
          GUI.Affects.Rows[GUI.Affects.current_row].Labels[GUI.Affects.current_column]:setStyleSheet(
            GUI.Affects.IconCSS:getCSS()
          )
          GUI.Affects.Rows[GUI.Affects.current_row].Labels[GUI.Affects.current_column]:show()
          GUI.Affects.current_column = GUI.Affects.current_column + 1
          if GUI.Affects.current_column &gt; GUI.Affects.num_icons_row then
            GUI.Affects.current_column = 1
            GUI.Affects.current_row = GUI.Affects.current_row + 1
          end
        end
      end
    end
  end
end</script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Group</name>
						<packageName></packageName>
						<script>function GUI.init_group()
  GUI.GroupConsole =
    Geyser.MiniConsole:new(
      {name = "GUI.GroupConsole", x = 0, y = 0, width = "100%", height = "100%"},
      GUI.tabbedInfoWindow["Groupcenter"]
    )
  GUI.GroupConsole:setColor({r=0, b=0, g=0, a=0})
	GUI.tabbedInfoWindow["Group"]:hide()
end

function GUI.updateGroup()
  GUI.group_data = {}
  -- Remove the player from the group data if toggled off.
  local j = 1
  local status_msg
  for i = 1, #msdp.GROUP do
    if GUI.toggles.includeInGroup == false and msdp.GROUP[i].NAME ~= msdp.CHARACTER_NAME then
      while GUI.group_data[j] ~= nil do
        j = j + 1
      end
      GUI.group_data[j] = {}
      GUI.group_data[j].IS_LEADER = msdp.GROUP[i].IS_LEADER
      GUI.group_data[j].NAME = msdp.GROUP[i].NAME
      GUI.group_data[j].HEALTH = msdp.GROUP[i].HEALTH
      GUI.group_data[j].HEALTH_MAX = msdp.GROUP[i].HEALTH_MAX
      GUI.group_data[j].MOVEMENT = msdp.GROUP[i].MOVEMENT
      GUI.group_data[j].MOVEMENT_MAX = msdp.GROUP[i].MOVEMENT_MAX
      GUI.group_data[j].LEVEL = msdp.GROUP[i].LEVEL
      GUI.group_data[j].CLASS_STRING = msdp.GROUP[i].CLASS_STRING
  -- Add the player from the group data if toggled on.
		elseif GUI.toggles.includeInGroup == true then
      while GUI.group_data[j] ~= nil do
        j = j + 1
      end
      GUI.group_data[j] = {}
      GUI.group_data[j].IS_LEADER = msdp.GROUP[i].IS_LEADER
      GUI.group_data[j].NAME = msdp.GROUP[i].NAME
      GUI.group_data[j].HEALTH = msdp.GROUP[i].HEALTH
      GUI.group_data[j].HEALTH_MAX = msdp.GROUP[i].HEALTH_MAX
      GUI.group_data[j].MOVEMENT = msdp.GROUP[i].MOVEMENT
      GUI.group_data[j].MOVEMENT_MAX = msdp.GROUP[i].MOVEMENT_MAX
      GUI.group_data[j].LEVEL = msdp.GROUP[i].LEVEL
      GUI.group_data[j].CLASS_STRING = msdp.GROUP[i].CLASS_STRING	
    end -- if/elseif
  end -- for
	clearUserWindow("GUI.GroupConsole")
  num_grouped = #GUI.group_data
  for i = 1, num_grouped do
    -- Build the group member data and send it.
    --	+----------------------------------+
    --  | [level] Name                     |
    --  | hp: 90%  |=================    | |
    --  | Crown (leader), Shield (Tank)    |
    --  +----------------------------------+		
    if GUI.group_data[i].IS_LEADER == "1" then
      status_msg = "&lt;green&gt;LEADER"
    else
      status_msg = ""
    end
    local pct_health
    local health = tonumber(GUI.group_data[i].HEALTH)
    local max_health = tonumber(GUI.group_data[i].HEALTH_MAX)
    if health &gt; 0 then
      pct_health = (health / max_health) * 100
    else
      pct_health = 0
    end
    if pct_health &gt; 60 then
      hp_color = "&lt;green&gt;"
    elseif pct_health &gt; 15 then
        hp_color = "&lt;yellow&gt;"
      else
        hp_color = "&lt;red&gt;"
      end
			
			local formatted_level = "[" .. GUI.group_data[i].LEVEL .. "]"
			local formatted_name = GUI.group_data[i].NAME 
			formatted_level = string.cut(formatted_level.."   ", 4)			
			formatted_name = string.cut(formatted_name.."                     ", 23)
			
			local member = string.format("&lt;green&gt;[%2s] &lt;white&gt;%-23s: &lt;green&gt;[%s%3s&lt;green&gt;/%-3s]H [%3s/%-3s]V\n"
																	 , GUI.group_data[i].LEVEL
																	 , GUI.group_data[i].NAME
																	 , hp_color
																	 , GUI.group_data[i].HEALTH
																	 , GUI.group_data[i].HEALTH_MAX
																	 , GUI.group_data[i].MOVEMENT
																	 , GUI.group_data[i].MOVEMENT_MAX)
      GUI.GroupConsole:cecho(member)
      --GUI.GroupConsole:cecho(status_msg .. "\n")
    end
  end


</script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Player</name>
						<packageName></packageName>
						<script>function GUI.init_player()
  -- Set stylesheet with : qproperty-alignment: 'AlignLeft | AlignTop';
	GUI.tabbedInfoWindow["Playercenter"]:setStyleSheet([[
		qproperty-alignment: 'AlignLeft | AlignTop';
	]]);

end

function GUI.updatePlayer()
    GUI.tabbedInfoWindow["Playercenter"]:echo(
		[[&lt;p style="font-size:16px;font-style:normal;font-family: 'Bitstream Vera Sans Mono'"&gt;]] .. 
    (msdp.CHARACTER_NAME or "Unknown") ..		
    [[&lt;br/&gt;Level ]] ..
    (msdp.LEVEL or "Unknown") ..
    [[ ]] ..
    (msdp.CLASS or "Unknown") ..
    [[&lt;br/&gt;]] ..
    (msdp.RACE or "Unknown") ..
    [[&lt;br/&gt;]] ..    
    [[&lt;br/&gt;Str: ]] ..
    (msdp.STR or "Unknown") ..
    [[&lt;br/&gt;Dex: ]] ..
    (msdp.DEX or "Unknown") ..
    [[&lt;br/&gt;Con: ]] ..
    (msdp.CON or "Unknown") ..
    [[&lt;br/&gt;Int: ]] ..
    (msdp.INT or "Unknown") ..
    [[&lt;br/&gt;Wis: ]] ..
    (msdp.WIS or "Unknown") ..
    [[&lt;br/&gt;Cha: ]] ..
    (msdp.CHA or "Unknown") ..
    [[&lt;br/&gt;]] ..
    [[&lt;br/&gt;AC: ]] ..
    (msdp.AC or "Unknown") ..
    [[&lt;br/&gt;]] ..
    [[&lt;br/&gt;Gold: ]] ..
    (msdp.MONEY or "Unknown") ..
    [[&lt;br/&gt;]] ..
		[[&lt;/font&gt;&lt;/p&gt;]]
  )

end</script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Buttons</name>
						<packageName></packageName>
						<script>GUI.buttonWindow =
  GUI.buttonWindow or
  {
    button = {"Legend", "Mudlet", "ASCII"},
    color1 = "rgba(0,0,0,0)",
    color2 = "rgba(0,0,0,0)",
    width = "100%",
    height = "100%",
		roomOrLegend = "Room",
		mudletOrAscii = "Mudlet",
		--# of chars wide text needs to be
		legendWidth = 50,
		--# of lines needed to display text
		legendHeight = 11,
  }

--Determine font size for Legend
function GUI.buttonWindow.adjustLegendFont()
  local w = GUI.buttonWindow.Legend.get_width()
  local h = GUI.buttonWindow.Legend.get_height()
  local font_size = 8
  repeat
    font_size = font_size + 1
    local width, height = calcFontSize(font_size)
    width = width * GUI.buttonWindow.legendWidth
    height = height * GUI.buttonWindow.legendHeight
  until (w &lt; width) or (h &lt; height)
	GUI.buttonWindow.Legend_font_size = font_size - 1
  setMiniConsoleFontSize("GUI.buttonWindow.Legend", GUI.buttonWindow.Legend_font_size)
end

--Button callback for Legend
function GUI.buttonWindow.legendClick()	
  --Toggle For Room Info/Legend
	if GUI.buttonWindow.roomOrLegend == "Room" then
		GUI.buttonWindow.roomInfo:hide()
    GUI.buttonWindow.Legend:show()
		GUI.buttonWindow.roomOrLegend = "Legend"
		GUI.updateRoom()
		GUI.buttonWindow.Legendbutton:echo("Legend", "yellow", "c")
	elseif GUI.buttonWindow.roomOrLegend == "Legend" then
		GUI.updateLegend()
		GUI.buttonWindow.roomInfo:show()
    GUI.buttonWindow.Legend:hide()
		GUI.buttonWindow.roomOrLegend = "Room"
		GUI.buttonWindow.Legendbutton:echo("Legend", "white", "c")
	end
end

--Button callback for Mudlet map.
function GUI.buttonWindow.mudletClick()
    map.minimapcontainer:hide()
    map.container:show()
	  GUI.buttonWindow.mudletOrAscii = "Mudlet"
		GUI.buttonWindow.Mudletbutton:echo("Mudlet", "yellow", "c")
		GUI.buttonWindow.ASCIIbutton:echo("ASCII", "white", "c")
end

--Button callback for ASCII map.
function GUI.buttonWindow.asciiClick()
    map.minimapcontainer:show()
    map.container:hide()
		GUI.buttonWindow.mudletOrAscii = "ASCII"
		GUI.buttonWindow.ASCIIbutton:echo("ASCII", "yellow", "c")
		GUI.buttonWindow.Mudletbutton:echo("Mudlet", "white", "c")
end

--Button Init
function GUI.buttonWindow.init()
  GUI.buttonWindowCSS =
    CSSMan.new(
      [[font-family: Tahoma, Geneva, sans-serif; 
			border-image: url(]] ..
      getMudletHomeDir():gsub("\\", "/") ..
      [[/LuminariGUI/images/buttons/button.png) 0 0 0 0 stretch stretch;]]
    )
		
--Entire Box3 Container for Buttons
  GUI.buttonWindow.container =
    Geyser.Container:new(
      {
        name = "GUI.buttonWindow.container",
        x = 9,
        y = 9,
        width = GUI.Box3:get_width() - 18,
        height = GUI.Box3:get_height() - 18,
      },
      GUI.Box3
    )

--Room Info
  GUI.buttonWindow.roomInfo = 
	Geyser.Label:new(
		{
		  name = "GUI.buttonWindow.roomInfo",
			x = 9,
			y = 9,
			width = GUI.Box5:get_width()- 18,
			height = GUI.Box5:get_height() - 18,
			},
			GUI.Box5
		)

--Map Legend
  GUI.buttonWindow.Legend = 
	Geyser.MiniConsole:new(
		{
		  name = "GUI.buttonWindow.Legend",
			x = 9,
			y = 9,
			width = GUI.Box5:get_width()- 18,
			height = GUI.Box5:get_height() - 18,
			color = "black",
			},
			GUI.Box5
		)
		GUI.buttonWindow.adjustLegendFont()
		
--Button Container
  GUI.buttonWindow.buttonContainer =
    Geyser.HBox:new(
      {name = "GUI.buttonWindow.buttonContainer", x = 0, y = 0, width = "100%", height = "100%"},
      GUI.buttonWindow.container
    )

--Draw Buttons
  for k, v in pairs(GUI.buttonWindow.button) do
    GUI.buttonWindow[v .. "button"] =
      Geyser.Label:new({name = "GUI.buttonWindow." .. v .. "button"}, GUI.buttonWindow.buttonContainer)
    GUI.buttonWindow[v .. "button"]:setStyleSheet(GUI.buttonWindowCSS:getCSS())
    GUI.buttonWindow[v .. "button"]:echo("&lt;center&gt;" .. v)
  end

--Legend Call Back
  GUI.buttonWindow.Legendbutton:setClickCallback("GUI.buttonWindow.legendClick")		
--Mudlet/ASCII Callback
  GUI.buttonWindow.Mudletbutton:setClickCallback("GUI.buttonWindow.mudletClick")	
  GUI.buttonWindow.ASCIIbutton:setClickCallback("GUI.buttonWindow.asciiClick")	
	
--Show Room Info on Startup
	GUI.buttonWindow.roomInfo:show()
  GUI.buttonWindow.Legend:hide()
	
--Populating Legend
	GUI.updateLegend()

--[[Will error line 33 if we call mudletClick callback
  so handling echo this way since Mudlet loads its map
  by default. ]]
	GUI.buttonWindow.Mudletbutton:echo("Mudlet", "yellow", "c")
	GUI.buttonWindow.ASCIIbutton:echo("ASCII", "white", "c")
	
end </script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Room Info/Legend</name>
						<packageName></packageName>
						<script>
function GUI.updateRoom()
  if msdp.ROOM.ENVIRONMENT == "Wilderness" then
    GUI.buttonWindow.roomInfo:echo(
		  [[&lt;p style="font-size:18px;font-style:normal;font-family: 'Bitstream Vera Sans Mono'"&gt;]] .. 	
      [[ ]] ..
      (msdp.ROOM.ENVIRONMENT or "Unknown") ..
      [[&lt;br/&gt;&amp;nbsp;Coords: ]] ..
      ("("..msdp.ROOM.COORDS.X..", "..msdp.ROOM.COORDS.Y..")" or "Unknown") ..
      [[&lt;br/&gt;Terrain: ]] ..
      (msdp.ROOM.TERRAIN or "Unknown") ..
      [[&lt;br/&gt;]] ..
		  [[&lt;/font&gt;&lt;/p&gt;]]
    )	
	else
    GUI.buttonWindow.roomInfo:echo(
		  [[&lt;p style="font-size:18px;font-style:normal;font-family: 'Bitstream Vera Sans Mono'"&gt;]] .. 	
      [[&lt;br/&gt;&amp;nbsp;&amp;nbsp;&amp;nbsp;Room: ]] ..
      (msdp.ROOM.NAME or "Unknown") ..
      [[&lt;br/&gt;&amp;nbsp;Room #: ]] ..
      (msdp.ROOM.VNUM or "Unknown") ..
      [[&lt;br/&gt;Terrain: ]] ..
      (msdp.ROOM.TERRAIN or "Unknown") ..
      [[&lt;br/&gt;&amp;nbsp;&amp;nbsp;&amp;nbsp;Area: ]] ..
      (msdp.ROOM.AREA or "Unknown") ..
	 -- [[&lt;br/&gt;&amp;nbsp;Levels: ]] ..
   -- (msdp.ROOM.WHATTT or "Unknown") ..
      [[&lt;br/&gt;]] ..
		  [[&lt;/font&gt;&lt;/p&gt;]]
    )
  end		
end
		
--Whattt to be replaced with MSDP or GMCP once coded in


function GUI.updateLegend()
  clearUserWindow("GUI.buttonWindow.Legend")
	GUI.buttonWindow.Legend:decho("\n &lt;102,0,0&gt; + &lt;255,255,255&gt; Up           &lt;0,102,102&gt;[&lt;0,255,255&gt;^&lt;0,102,102&gt;] &lt;255,255,255&gt;Air           &lt;0,102,102&gt;[&lt;255,0,255&gt;Y&lt;0,102,102&gt;] &lt;255,255,255&gt;UD Wild")
	GUI.buttonWindow.Legend:decho("\n &lt;102,0,0&gt; - &lt;255,255,255&gt; Down         &lt;0,102,102&gt;[&lt;0,0,102&gt;U&lt;0,102,102&gt;] &lt;255,255,255&gt;Underwater    &lt;0,102,102&gt;[&lt;102,0,102&gt;C&lt;0,102,102&gt;] &lt;255,255,255&gt;UD City")
	GUI.buttonWindow.Legend:decho("\n &lt;0,102,102&gt;[&lt;255,255,255&gt;&amp;&lt;0,102,102&gt;] &lt;255,255,255&gt;You          &lt;0,102,102&gt;[&lt;255,0,0&gt;X&lt;0,102,102&gt;] &lt;255,255,255&gt;Zone Entry    &lt;0,102,102&gt;[&lt;102,0,102&gt;.&lt;0,102,102&gt;] &lt;255,255,255&gt;UD Inside")  
	GUI.buttonWindow.Legend:decho("\n &lt;0,102,102&gt;[&lt;192,192,192&gt;.&lt;0,102,102&gt;] &lt;255,255,255&gt;Inside       &lt;0,102,102&gt;[&lt;51,51,51&gt;|&lt;0,102,102&gt;] &lt;255,255,255&gt;Road N-S      &lt;0,102,102&gt;[&lt;102,0,102&gt;~&lt;0,102,102&gt;] &lt;255,255,255&gt;UD Water") 
	GUI.buttonWindow.Legend:decho("\n &lt;0,102,102&gt;[&lt;102,102,102&gt;C&lt;0,102,102&gt;] &lt;255,255,255&gt;City         &lt;0,102,102&gt;[&lt;51,51,51&gt;-&lt;0,102,102&gt;] &lt;255,255,255&gt;Road E-W      &lt;0,102,102&gt;[&lt;255,0,255&gt;=&lt;0,102,102&gt;] &lt;255,255,255&gt;UD D Water")  
	GUI.buttonWindow.Legend:decho("\n &lt;0,102,102&gt;[&lt;0,102,0&gt;,&lt;0,102,102&gt;] &lt;255,255,255&gt;Field        &lt;0,102,102&gt;[&lt;51,51,51&gt;+&lt;0,102,102&gt;] &lt;255,255,255&gt;Intersect     &lt;0,102,102&gt;[&lt;102,0,102&gt;^&lt;0,102,102&gt;] &lt;255,255,255&gt;UD Air")  
	GUI.buttonWindow.Legend:decho("\n &lt;0,102,102&gt;[&lt;0,255,0&gt;Y&lt;0,102,102&gt;] &lt;255,255,255&gt;Forest       &lt;0,102,102&gt;[&lt;255,255,0&gt;.&lt;0,102,102&gt;] &lt;255,255,255&gt;Desert        &lt;0,102,102&gt;[&lt;255,0,0&gt;.&lt;0,102,102&gt;] &lt;255,255,255&gt;Lava")  
	GUI.buttonWindow.Legend:decho("\n &lt;0,102,102&gt;[&lt;102,102,0&gt;^&lt;0,102,102&gt;] &lt;255,255,255&gt;Hills        &lt;0,102,102&gt;[&lt;0,0,255&gt;o&lt;0,102,102&gt;] &lt;255,255,255&gt;Ocean         &lt;0,102,102&gt;[&lt;102,102,0&gt;|&lt;0,102,102&gt;] &lt;255,255,255&gt;D Rd N-S")  
	GUI.buttonWindow.Legend:decho("\n &lt;0,102,102&gt;[&lt;102,0,0&gt;m&lt;0,102,102&gt;] &lt;255,255,255&gt;Mountain     &lt;0,102,102&gt;[&lt;255,0,255&gt;,&lt;0,102,102&gt;] &lt;255,255,255&gt;Marsh         &lt;0,102,102&gt;[&lt;102,102,0&gt;-&lt;0,102,102&gt;] &lt;255,255,255&gt;D Rd E-W")  
	GUI.buttonWindow.Legend:decho("\n &lt;0,102,102&gt;[&lt;0,102,102&gt;~&lt;0,102,102&gt;] &lt;255,255,255&gt;Water        &lt;0,102,102&gt;[&lt;255,0,0&gt;M&lt;0,102,102&gt;] &lt;255,255,255&gt;High Mount    &lt;0,102,102&gt;[&lt;102,102,0&gt;+&lt;0,102,102&gt;] &lt;255,255,255&gt;D Inters") 
	GUI.buttonWindow.Legend:decho("\n &lt;0,102,102&gt;[&lt;0,0,102&gt;=&lt;0,102,102&gt;] &lt;255,255,255&gt;Deep Water   &lt;0,102,102&gt;[&lt;51,51,51&gt;.&lt;0,102,102&gt;] &lt;255,255,255&gt;Planes        &lt;0,102,102&gt;[&lt;51,51,51&gt;C&lt;0,102,102&gt;] &lt;255,255,255&gt;Cave") 
end</script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>DrawFrames</name>
						<packageName></packageName>
						<script>function GUI.draw_frames()
  createFrame(GUI.Box2)
	createFrame(GUI.Box3)
  createFrame(GUI.Box4)
  createFrame(GUI.Box5)
  --Commenting out box 6 frames until skill icons are finished.
	--createFrame(GUI.Box6)
  createFrame(GUI.Box7)
end</script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>MSDP</name>
						<packageName></packageName>
						<script>function GUI.onProtocolEnabled(_, protocol)
  if protocol == "MSDP" then
    print("[GUI] Initializing MSDP variables")
    sendMSDP("REPORT", "CHARACTER_NAME")
		sendMSDP("REPORT", "RACE")
		sendMSDP("REPORT", "CLASS")
		sendMSDP("REPORT", "ALIGNMENT")
		sendMSDP("REPORT", "LEVEL")
		sendMSDP("REPORT", "STR")
		sendMSDP("REPORT", "DEX")
		sendMSDP("REPORT", "CON")
		sendMSDP("REPORT", "INT")
		sendMSDP("REPORT", "WIS")
		sendMSDP("REPORT", "CHA")
		sendMSDP("REPORT", "AC")
		sendMSDP("REPORT", "MONEY")
		sendMSDP("REPORT", "HEALTH")
    sendMSDP("REPORT", "HEALTH_MAX")
    sendMSDP("REPORT", "OPPONENT_HEALTH")
    sendMSDP("REPORT", "OPPONENT_HEALTH_MAX")
    sendMSDP("REPORT", "OPPONENT_NAME")
    sendMSDP("REPORT", "MOVEMENT")
    sendMSDP("REPORT", "MOVEMENT_MAX")
    sendMSDP("REPORT", "EXPERIENCE")
    sendMSDP("REPORT", "EXPERIENCE_TNL")
    sendMSDP("REPORT", "ACTIONS")
		sendMSDP("REPORT", "GROUP")
		--sendMSDP("REPORT", "INVENTORY")
		sendMSDP("REPORT", "AFFECTS")
  end
end

function GUI.updateHealthGauge()
  -- This function handles the following events:
  --   msdp.HEALTH
  --   msdp.HEALTH_MAX
  -- Calculate a percentage	
  local pct_health
  local health = tonumber(msdp.HEALTH)
  local max_health = tonumber(msdp.HEALTH_MAX)
	local overfilled = false
	
	if health &gt; max_health then
	  health = max_health
		overfilled = true
	end
  if health &gt; 0 then
    pct_health = (health / max_health) * 100
  else
    pct_health = 0
  end
  GUI.Health.front:echo("H: " .. msdp.HEALTH .. "/" .. msdp.HEALTH_MAX)
  GUI.Health.back:echo("H: " .. msdp.HEALTH .. "/" .. msdp.HEALTH_MAX)
	if pct_health &gt; 100 then
    GUI.Health:setValue(100, 100)
	else
    GUI.Health:setValue(pct_health, 100)
	end
end

function GUI.updateMovesGauge()
  -- This function handles the following events:
  --   msdp.MOVEMENT
  --   msdp.MOVEMENT_MAX
  -- Calculate a percentage	
  local pct_moves
  local moves = tonumber(msdp.MOVEMENT)
  local max_moves = tonumber(msdp.MOVEMENT_MAX)
  if moves &gt; 0 then
    pct_moves = (moves / max_moves) * 100
  else
    pct_moves = 0
  end
  GUI.Moves.front:echo("Mv: " .. msdp.MOVEMENT .. "/" .. msdp.MOVEMENT_MAX)
  GUI.Moves.back:echo("Mv: " .. msdp.MOVEMENT .. "/" .. msdp.MOVEMENT_MAX)
  GUI.Moves:setValue(pct_moves, 100)
end

function GUI.updateExperienceGauge()
  -- This function handles the following events:
  --   msdp.EXPERIENCE
  --   msdp.EXPERIENCE_MAX
  -- Calculate a percentage	
  local pct_xp
  local xp = tonumber(msdp.EXPERIENCE)
  local xp_tnl = tonumber(msdp.EXPERIENCE_TNL)
  if xp &gt; 0 then
    pct_xp = (xp / (xp + xp_tnl)) * 100
  else
    pct_xp = 0
  end
  GUI.Experience.front:echo(
    [[&lt;span style = "color: black"&gt;XP: ]] .. xp .. "/" .. (xp + xp_tnl) .. [[&lt;/span&gt;]]
  )
  GUI.Experience.back:echo([[&lt;span&gt;XP: ]] .. xp .. "/" .. (xp + xp_tnl) .. [[&lt;/span&gt;]])
	if pct_xp &gt; 100 then
	  GUI.Experience:setValue(100, 100)
	else
    GUI.Experience:setValue(pct_xp, 100)
	end
end

function GUI.updateEnemyGauge()
  -- This function handles the following events:
  --   msdp.OPPONENT_HEALTH
  --   msdp.OPPONENT_HEALTH_MAX	
  --   msdp.OPPONENT_NAME
  -- Show/Hide the gauge.
  if msdp.OPPONENT_NAME == "" then
    GUI.Enemy:hide()
  else
    GUI.Enemy:show()
  end
  -- Calculate a percentage	
  local pct_health
  local health = tonumber(msdp.OPPONENT_HEALTH)
  local max_health = tonumber(msdp.OPPONENT_HEALTH_MAX)
  if health &gt; 0 then
    pct_health = (health / max_health) * 100
  else
    pct_health = 0
  end
  GUI.Enemy.front:echo(msdp.OPPONENT_NAME .. ": " .. health .. "/" .. max_health)
  GUI.Enemy.back:echo(msdp.OPPONENT_NAME .. ": " .. health .. "/" .. max_health)
	if pct_health &gt; 100 then
	  GUI.Enemy:setValue(100, 100)
	else
    GUI.Enemy:setValue(pct_health, 100)
	end
end

function GUI.updateActionIcons()
  if (msdp.ACTIONS.STANDARD_ACTION == "1") then
    GUI.ActionIconCSS:set(
      "border-image",
      [[url("]] ..
      getMudletHomeDir():gsub("\\", "/") ..
      [[/LuminariGUI/images/action-standard.png");]]
    )
    GUI.StandardActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
  end
  if (msdp.ACTIONS.MOVE_ACTION == "1") then
    GUI.ActionIconCSS:set(
      "border-image",
      [[url("]] .. getMudletHomeDir():gsub("\\", "/") .. [[/LuminariGUI/images/action-move.png");]]
    )
    GUI.MoveActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
  end
  if (msdp.ACTIONS.SWIFT_ACTION == "1") then
    GUI.ActionIconCSS:set(
      "border-image",
      [[url("]] .. getMudletHomeDir():gsub("\\", "/") .. [[/LuminariGUI/images/action-swift.png");]]
    )
    GUI.SwiftActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
  end
  if (msdp.ACTIONS.STANDARD_ACTION == "0") then
    GUI.ActionIconCSS:set(
      "border-image",
      [[url("]] ..
      getMudletHomeDir():gsub("\\", "/") ..
      [[/LuminariGUI/images/action-standard-50.png");]]
    )
    GUI.StandardActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
  end
  if (msdp.ACTIONS.MOVE_ACTION == "0") then
    GUI.ActionIconCSS:set(
      "border-image",
      [[url("]] ..
      getMudletHomeDir():gsub("\\", "/") ..
      [[/LuminariGUI/images/action-move-50.png");]]
    )
    GUI.MoveActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
  end
  if (msdp.ACTIONS.SWIFT_ACTION == "0") then
    GUI.ActionIconCSS:set(
      "border-image",
      [[url("]] ..
      getMudletHomeDir():gsub("\\", "/") ..
      [[/LuminariGUI/images/action-swift-50.png");]]
    )
    GUI.SwiftActionIcon:setStyleSheet(GUI.ActionIconCSS:getCSS())
  end
end
</script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Config</name>
						<packageName></packageName>
						<script>  -- This needs to be outside the config, since YATCO needs to have the boxes set.
	GUI.image_location = getMudletHomeDir():gsub("\\", "/") .. "/LuminariGUI/images/"
	GUI.init_background()
  GUI.set_borders()
  GUI.init_boxes()
	
function GUI.init()
  GUI.AffectIcons = GUI.AffectIcons or {}
  GUI.Affects = GUI.Affects or {}
  GUI.Affects.Rows = GUI.Affects.Rows or {}
  GUI.Affects.Modes = GUI.Affects.Modes or {}
  --
  GUI.init_gauges()
  GUI.init_action_icons()
  --GUI.init_header_icons()
  GUI.tabbedInfoWindow.init()
  GUI.init_group()
  GUI.Affects.init()
  GUI.draw_frames()
	GUI.buttonWindow.init()
  GUI.init_castConsole()
  GUI.styleScrollbar()
	
	
  --
  registerAnonymousEventHandler("msdp.GROUP", "GUI.updateGroup")
  registerAnonymousEventHandler("msdp.AFFECTS", "GUI.updateAffectIcons")
	--registerAnonymousEventHandler("msdp.AFFECTS", "GUI.updateSLAffects")
  --registerAnonymousEventHandler("sysConnectionEvent", "GUI.eventHandler")
  registerAnonymousEventHandler("sysProtocolEnabled", "GUI.onProtocolEnabled")
  registerAnonymousEventHandler("msdp.HEALTH", "GUI.updateHealthGauge")
  registerAnonymousEventHandler("msdp.HEALTH_MAX", "GUI.updateHealthGauge")
  registerAnonymousEventHandler("msdp.OPPONENT_HEALTH", "GUI.updateEnemyGauge")
  registerAnonymousEventHandler("msdp.OPPONENT_HEALTH_MAX", "GUI.updateEnemyGauge")
  registerAnonymousEventHandler("msdp.OPPONENT_NAME", "GUI.updateEnemyGauge")
  registerAnonymousEventHandler("msdp.MOVEMENT", "GUI.updateMovesGauge")
  registerAnonymousEventHandler("msdp.MOVEMENT_MAX", "GUI.updateMovesGauge")
  registerAnonymousEventHandler("msdp.EXPERIENCE", "GUI.updateExperienceGauge")
  registerAnonymousEventHandler("msdp.EXPERIENCE_TNL", "GUI.updateExperienceGauge")
  registerAnonymousEventHandler("msdp.ACTIONS", "GUI.updateActionIcons")
  registerAnonymousEventHandler("msdp.CHARACTER_NAME", "GUI.updatePlayer")
  registerAnonymousEventHandler("msdp.RACE", "GUI.updatePlayer")
  registerAnonymousEventHandler("msdp.CLASS", "GUI.updatePlayer")
  registerAnonymousEventHandler("msdp.ALIGNMENT", "GUI.updatePlayer")
  registerAnonymousEventHandler("msdp.LEVEL", "GUI.updatePlayer")
  registerAnonymousEventHandler("msdp.STR", "GUI.updatePlayer")
  registerAnonymousEventHandler("msdp.DEX", "GUI.updatePlayer")
  registerAnonymousEventHandler("msdp.CON", "GUI.updatePlayer")
  registerAnonymousEventHandler("msdp.INT", "GUI.updatePlayer")
  registerAnonymousEventHandler("msdp.WIS", "GUI.updatePlayer")
  registerAnonymousEventHandler("msdp.CHA", "GUI.updatePlayer")
  registerAnonymousEventHandler("msdp.AC", "GUI.updatePlayer")
  registerAnonymousEventHandler("msdp.MONEY", "GUI.updatePlayer")
  registerAnonymousEventHandler("msdp.ROOM", "GUI.updateRoom")
end

registerAnonymousEventHandler("sysLoadEvent", "GUI.init")
registerAnonymousEventHandler("sysInstall", "GUI.init")</script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Custom Scrollbar</name>
						<packageName></packageName>
						<script>function GUI.styleScrollbar()
  local background_color = "#202020"
  local border_color = "#515151"
  setProfileStyleSheet(
    [[
  QScrollBar:vertical {
     background: ]] ..
    background_color ..
    [[;
     width: 15px;
     margin: 22px 0 22px 0;
  }
  QScrollBar::handle:vertical {
     background-color: ]] ..
    background_color ..
    [[;
     min-height: 20px;
     border-width: 2px;
     border-style: solid;
     border-color: ]] ..
    border_color ..
    [[;
     border-radius: 7px;
  }
  QScrollBar::add-line:vertical {
   background-color: ]] ..
    background_color ..
    [[;
   border-width: 2px;
   border-style: solid;
   border-color: ]] ..
    border_color ..
    [[;
   border-bottom-left-radius: 7px;
   border-bottom-right-radius: 7px;
        height: 15px;
        subcontrol-position: bottom;
        subcontrol-origin: margin;
  }
  QScrollBar::sub-line:vertical {
   background-color: ]] ..
    background_color ..
    [[;
   border-width: 2px;
   border-style: solid;
   border-color: ]] ..
    border_color ..
    [[;
   border-top-left-radius: 7px;
   border-top-right-radius: 7px;
        height: 15px;
        subcontrol-position: top;
        subcontrol-origin: margin;
  }
  QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
     background: white;
     width: 4px;
     height: 3px;
  }
  QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
     background: none;
  }
]]
  )
end</script>
						<eventHandlerList />
					</Script>
					<Script isActive="yes" isFolder="no">
						<name>Delete Line and  Prompt</name>
						<packageName></packageName>
						<script>function deleteLineP()
  deleteLine()
  tempLineTrigger(1, 1, [[if isPrompt() then deleteLine() end]])
end</script>
						<eventHandlerList />
					</Script>
				</ScriptGroup>
			</ScriptGroup>
			<ScriptGroup isActive="yes" isFolder="yes">
				<name>YATCOConfig</name>
				<packageName>YATCOConfig</packageName>
				<script></script>
				<eventHandlerList />
				<ScriptGroup isActive="yes" isFolder="yes">
					<name>YATCOCONFIG</name>
					<packageName></packageName>
					<script>demonnic = demonnic or {}
demonnic.chat = demonnic.chat or {}
demonnic.chat.config = demonnic.chat.config or {}</script>
					<eventHandlerList />
					<Script isActive="yes" isFolder="no">
						<name>Configuration Options</name>
						<packageName></packageName>
						<script>--[[
This is where all of the configuration options can be set. 
Anything I've put in this script object can be changed, but please do pay attention to what you're doing.
If you change one of the values to something it shouldn't be, you could break it. 
]]

--This is where you tell it to use tabbed chat.
demonnic.chat.use = true

--[[
This is where you put what container to put the tabbed chat stuff into. Make it
equal to the actual container object you want it in, or false for none. Defaults to false
Which is to say if you want to put the tabbed chat stuff into a container made using 
uiRight = Geyser.Container:new()

you would put

demonnic.chat.useContainer = uiRight

and if you don't want it in a container you would put

demonnic.chat.useContainer = false
]]

demonnic.chat.useContainer = GUI.chatContainer

--[[
The timestamp option is set here.
Set to false if you do not want any timestamps displayed for chat.
If you do want it displayed, set to the string for the format you wish. 
see http://wiki.mudlet.org/w/Manual:Lua_Functions#getTime for information
how to format the string
]]
--demonnic.chat.config.timestamp = "HH:mm:ss"
demonnic.chat.config.timestamp = false

--[[ Should we use our own colors for the timestamp?
Set to true if you want to specify foreground and background colors
for the timestamp.
Set to false if you want the timestamps background and foreground
colors to match that of the mud output.
]]
demonnic.chat.config.timestampCustomColor = false
--[[
and what foreground color? You can either use one of the 'named' colors
(see http://wiki.mudlet.org/images/c/c3/ShowColors.png for available colors)
demonnic.chat.config.timestampFG = "slate_grey"

Or you can use a table of R,G,B values. 
demonnic.chat.config.timestampFG = {
  255,
    0,
    0,
}
then the foreground for the timestamp would be 255 read, 100 green, and 0 blue
]]
demonnic.chat.config.timestampFG = "red"

--and background? Same rules as for the foreground above
demonnic.chat.config.timestampBG = "blue"

--[[
This is where you say what corner of the screen you want the tabbed chat on
Valid settings are "topright", "topleft", "bottomright", "bottomleft"
]]--
demonnic.chat.config.location = "topright"

--[[
This is a table of channels you would like.
AKA the place you tell the script what tabs you want.
Each entry must be a string. The defaults should be a pretty effective guide.
]]

demonnic.chat.config.channels = {
  "All",
  "Tell",
  "Chat",
	"Wiz",
	"Group",
	"Congrats",
	"Auction",  
}


--Set this to the name of the channel you want to have everything sent to. 
--Per the default, this would be the "All" channel. If you have a different name for it:
--
--demonnic.chat.config.Alltab = "Bucket"  
--
--And if you don't want it turned on at all:
--
--demonnic.chat.config.Alltab = false

demonnic.chat.config.Alltab = "All"

--Set this to the name of the channel you want to display your map. Set to "" if you
--don't want to display the map in your YATCO tabs
demonnic.chat.config.Maptab = ""


---------------------------------------------------------------------------------
--                                                                             --
--The infamous blinking stuff!!!                                               --
--                                                                             --
---------------------------------------------------------------------------------

--[[
Do you want tabs to blink when you get new messages, until you click on the tab?
True if yes, false if no.
]]
demonnic.chat.config.blink = true

--How long (in seconds) between blinks? For example, 1 would mean a 1 second pause in between blinks.
demonnic.chat.config.blinkTime = 3

--Blink if the bucket tab ("All" by default, but configured above) is in focus?
demonnic.chat.config.blinkFromAll = false




--Font size for the chat messages

demonnic.chat.config.fontSize = getFontSize("main")

--[[
Should we preserve the formatting of the text. 
Or should we set the background of it to match the window color?
Set this to false if you want the background for all chat to match the background of the window.
Useful if you change the background from black, and don't like the way the pasted chat makes blocks in it
]]

demonnic.chat.config.preserveBackground = true

--[[
Gag the chat lines in the main window?
defaults to false, set to true if you want to gag.
]]

demonnic.chat.config.gag = false

--[[
Number of lines of chat visible at once. 
Will determine how tall the window for the chats is.
]]

demonnic.chat.config.lines = 22

--[[
Number of characters to wrap the chatlines at.
This will also determine how wide the chat windows are.
]]

demonnic.chat.config.width = getColumnCount("main")

--[[
Set the color for the active tab. R,G,B format.
The default here is a brightish green
]]

demonnic.chat.config.activeColors = {
  r = 0,
  g = 180,
  b = 0,
}

--[[
Set the color for the inactive tab. R,G,B format.
The default here is a drab grey
]]

demonnic.chat.config.inactiveColors = {
  r = 60,
  g = 60,
  b = 60,
}

--[[
Set the color for the chat window itself. R,G,B format.
Defaulted to the black of my twisted hardened soul. Or something.
]]

demonnic.chat.config.windowColors = {
  r = 0,
  g = 0,
  b = 0,
}

--[[
Set the color for the text on the active tab. Uses color names.
Set the default to purple. So the tab you're looking at, by default will be purple on bright green. 
Did I mention I'm a bit colorblind?
]]

demonnic.chat.config.activeTabText = "yellow"

--[[
Set the color for the text on the inactive tabs. Uses color names.
Defaulted this to white. So the tabs you're not looking at will be white text on boring grey background.
]]

demonnic.chat.config.inactiveTabText = "white"

--[[
have to make sure a currentTab is set... 
so we'll use the one for the bucket, or the first one in the channels table
Or, you know... what it's currently set to, if it's already set.
]]
demonnic.chat.currentTab = demonnic.chat.currentTab or demonnic.chat.config.Alltab or demonnic.chat.config.channels[1]
</script>
						<eventHandlerList />
					</Script>
				</ScriptGroup>
			</ScriptGroup>
			<ScriptGroup isActive="yes" isFolder="yes">
				<name>YATCO</name>
				<packageName>YATCO</packageName>
				<script></script>
				<eventHandlerList />
				<ScriptGroup isActive="yes" isFolder="yes">
					<name>Demonnic</name>
					<packageName></packageName>
					<script></script>
					<eventHandlerList />
					<ScriptGroup isActive="yes" isFolder="yes">
						<name>Shared</name>
						<packageName></packageName>
						<script>--Bootstrapping variables/etc. Don't touch this unless you really know what you're doing

--I mean it. I'll point. AND laugh. loudly. 
demonnic = demonnic or {}
demonnic.config = demonnic.config or {}
demonnic.balances = demonnic.balances or {}
demonnic.balances.balance = demonnic.balances.balance or 1
demonnic.balances.equilibrium = demonnic.balances.equilibrium or 1
demonnic.debug = demonnic.debug or {}
demonnic.debug.active = demonnic.debug.active or nil
demonnic.debug.categories = demonnic.debug.categories or { }


function demonnic:echo(msg)
 cecho(string.format("\n&lt;blue&gt;(&lt;green&gt;Demonnic&lt;blue&gt;):&lt;white&gt; %s", msg))
end</script>
						<eventHandlerList />
						<Script isActive="yes" isFolder="no">
							<name>Debugging</name>
							<packageName></packageName>
							<script>--Adds debugging functionality 

function demonnic:Debug(category,debugData)
   if category then
      if table.contains(demonnic.debug.categories, category) then
         if type(debugData) == "table" then
            demonnic:echo("&lt;red&gt;DEBUG " .. category .. ":&lt;white&gt;")
            display(debugData)
         elseif type(debugData) == "string" or type(debugData) == "number" then
            demonnic:echo("&lt;red&gt;DEBUG " .. category .. ":&lt;white&gt; " .. debugData .. "\n" )
         else
            demonnic:echo("&lt;red&gt;DEBUG " .. category .. ":&lt;white&gt; " .. tostring(debugData) .. "\n" )
         end
      end
   else
      if type(debugData) == "table" then
         demonnic:echo("&lt;red&gt;DEBUG:&lt;white&gt;")
         display(debugData)
      elseif type(debugData) == "string" or type(debugData) == "number" then
         demonnic:echo("&lt;red&gt;DEBUG:&lt;white&gt; " .. debugData)
      else
         demonnic:echo("&lt;red&gt;DEBUG:&lt;white&gt; " .. tostring(debugData))
      end
   end
end

function demonnic:printDebug(category, debugData)
   if not demonnic.debug.active then return end
   demonnic:Debug(category, debugData)
end

function demonnic:toggleDebug()
   if demonnic.debug.active then demonnic.debug.active = nil
   else demonnic.debug.active = true
   end
   demonnic:echo("Debugging is currently " .. (( demonnic.debug.active and "&lt;green&gt;ON&lt;white&gt;") or "&lt;red&gt;OFF&lt;white&gt;"))
end

function demonnic:watchCategory( category )
   if table.contains(demonnic.debug.categories, category) then
      for i,v in ipairs(demonnic.debug.categories) do
         if v == category then
            table.remove(demonnic.debug.categories, i)
         end
      end
      demonnic:echo("No longer watching the '&lt;red&gt;"..category.."&lt;white&gt;' category.") 
   else
      table.insert(demonnic.debug.categories, category)
      demonnic:echo("Now watching the '&lt;red&gt;"..category.."&lt;white&gt;' category.")
   end
   demonnic:echo("Debugging is currently " .. (( demonnic.debug.active and "&lt;green&gt;ON&lt;white&gt;") or "&lt;red&gt;OFF&lt;white&gt;"))
end

function demonnic:listCategories()
   if #demonnic.debug.categories &gt; 0 then
      demonnic:echo("You are currently watching the following categories:\n" .. table.concat(demonnic.debug.categories,", ") )
   else
      demonnic:echo("You are not watching any debugs.")
   end
end
</script>
							<eventHandlerList />
						</Script>
						<Script isActive="yes" isFolder="no">
							<name>Geyser Additions</name>
							<packageName></packageName>
							<script>function Geyser.MiniConsole:clear()
   clearWindow(self.name)
end

function Geyser.MiniConsole:append()
  appendBuffer(self.name)
end</script>
							<eventHandlerList />
						</Script>
					</ScriptGroup>
					<ScriptGroup isActive="yes" isFolder="yes">
						<name>Tabbed Chat</name>
						<packageName></packageName>
						<script>--Do not remove the following lines. Or change them.
demonnic = demonnic or {}
demonnic.chat = demonnic.chat or {}
demonnic.chat.tabsToBlink = demonnic.chat.tabsToBlink or {}
demonnic.chat.tabs = demonnic.chat.tabs or {}
demonnic.chat.windows = demonnic.chat.windows or {}
if not demonnic.chat.config then
  cecho("&lt;red:white&gt;YOU DO NOT HAVE THE YATCO CONFIG PACKAGE IN PLACE. THINGS WILL NOT WORK AS EXPECTED\n\n")
  demonnic.chat.error = "NO CONFIG"
end</script>
						<eventHandlerList />
						<Script isActive="yes" isFolder="no">
							<name>Code</name>
							<packageName></packageName>
							<script>--[[
If the label callbacks ever decide to start taking a function which is part of a table, 0then this will change.
Or if it's modified to take actual functions. Anonymouse function clickcallback would be awfully nice.
]]

function demonnicChatSwitch(chat)
  local r = demonnic.chat.config.inactiveColors.r
  local g = demonnic.chat.config.inactiveColors.g
  local b = demonnic.chat.config.inactiveColors.b
  local newr = demonnic.chat.config.activeColors.r
  local newg = demonnic.chat.config.activeColors.g
  local newb = demonnic.chat.config.activeColors.b
  local oldchat = demonnic.chat.currentTab
  if demonnic.chat.currentTab ~= chat then
    demonnic.chat.windows[oldchat]:hide()
    demonnic.chat.tabs[oldchat]:setColor(r,g,b)
    demonnic.chat.tabs[oldchat]:echo(oldchat, demonnic.chat.config.inactiveTabText, "c")
    if demonnic.chat.config.blink and demonnic.chat.tabsToBlink[chat] then
      demonnic.chat.tabsToBlink[chat] = nil
    end
    if demonnic.chat.config.blink and chat == demonnic.chat.config.Alltab then
      demonnic.chat.tabsToBlink = {}
    end
  end
  demonnic.chat.tabs[chat]:setColor(newr,newg,newb)
  demonnic.chat.tabs[chat]:echo(chat, demonnic.chat.config.activeTabText, "c")
  demonnic.chat.windows[chat]:show()
  demonnic.chat.currentTab = chat  
end

function demonnic.chat:resetUI()
  demonnic.chat.container = demonnic.chat.useContainer or Geyser.Container:new(demonnic.chat[demonnic.chat.config.location]())
  demonnic.chat.tabBox = Geyser.HBox:new({
    x=0,
    y=0,
    width = "100%",
    height = "25px",
    name = "DemonChatTabs",
  },demonnic.chat.container)

end

function demonnic.chat:create()
  --reset the UI
  demonnic.chat:resetUI()
  --Set some variables locally to increase readability
  local r = demonnic.chat.config.inactiveColors.r
  local g = demonnic.chat.config.inactiveColors.g
  local b = demonnic.chat.config.inactiveColors.b
  local winr = demonnic.chat.config.windowColors.r
  local wing = demonnic.chat.config.windowColors.g
  local winb = demonnic.chat.config.windowColors.b

  --iterate the table of channels and create some windows and tabs
  for i,tab in ipairs(demonnic.chat.config.channels) do
    demonnic.chat.tabs[tab] = Geyser.Label:new({
      name=string.format("tab%s", tab),
    }, demonnic.chat.tabBox)
    demonnic.chat.tabs[tab]:echo(tab, demonnic.chat.config.inactiveTabText, "c")
    demonnic.chat.tabs[tab]:setColor(r,g,b)
    demonnic.chat.tabs[tab]:setClickCallback("demonnicChatSwitch", tab)
		
		demonnic.chat.tabs[tab]:setStyleSheet(
		 [[font-family: Tahoma, Geneva, sans-serif; 
		border-image: url(]] ..
    getMudletHomeDir():gsub("\\", "/") .. [[/LuminariGUI/images/buttons/button.png) 0 0 0 0 stretch stretch;]])
		
    demonnic.chat.windows[tab] = Geyser.MiniConsole:new({
--      fontSize = demonnic.chat.config.fontSize,
      x = 0,
      y = 25,
      height = "100%",
      width = "100%",
      name = string.format("win%s", tab),
    }, demonnic.chat.container)
    demonnic.chat.windows[tab]:setFontSize(demonnic.chat.config.fontSize)
    demonnic.chat.windows[tab]:setColor(winr,wing,winb)
    demonnic.chat.windows[tab]:setWrap(demonnic.chat.config.width)
    demonnic.chat.windows[tab]:hide()
  end
  if demonnic.chat.config.Maptab and demonnic.chat.config.Maptab ~= "" then
    demonnic.chat.mapWindow = Geyser.Mapper:new({
      x = 0,
      y = 0,
      height = "100%",
      width = "100%",
    }, demonnic.chat.windows[demonnic.chat.config.Maptab])
    demonnic.chat.windows[demonnic.chat.config.Maptab]:hide()
  end
  local showme = demonnic.chat.config.Alltab or demonnic.chat.config.channels[1]
  demonnicChatSwitch(showme)
  --start the blink timers, if enabled
  if demonnic.chat.config.blink and not demonnic.chat.blinkTimerOn then
    demonnic.chat:blink()
  end
end

function demonnic.chat:append(chat)
  local r = demonnic.chat.config.windowColors.r
  local g = demonnic.chat.config.windowColors.g
  local b = demonnic.chat.config.windowColors.b
  selectCurrentLine()
  local ofr,ofg,ofb = getFgColor()
  local obr,obg,obb = getBgColor()
  if demonnic.chat.config.preserveBackground then
    setBgColor(r,g,b)
  end
  copy()
  if demonnic.chat.config.timestamp then
    local timestamp = getTime(true, demonnic.chat.config.timestamp)
    local tsfg = {}
    local tsbg = {}
    local colorLeader = ""
    if demonnic.chat.config.timestampCustomColor then
      if type(demonnic.chat.config.timestampFG) == "string" then
        tsfg = color_table[demonnic.chat.config.timestampFG]
      else
        tsfg = demonnic.chat.config.timestampFG
      end
      if type(demonnic.chat.config.timestampBG) == "string" then
        tsbg = color_table[demonnic.chat.config.timestampBG]
      else
        tsbg = demonnic.chat.config.timestampBG
      end
      colorLeader = string.format("&lt;%s,%s,%s:%s,%s,%s&gt;",tsfg[1],tsfg[2],tsfg[3],tsbg[1],tsbg[2],tsbg[3])
    else
      colorLeader = string.format("&lt;%s,%s,%s:%s,%s,%s&gt;",ofr,ofg,ofb,obr,obg,obb)
    end
    local fullstamp = string.format("%s%s",colorLeader,timestamp)
      demonnic.chat.windows[chat]:decho(fullstamp)
      demonnic.chat.windows[chat]:echo(" ")
      if demonnic.chat.config.Alltab then 
        demonnic.chat.windows[demonnic.chat.config.Alltab]:decho(fullstamp)
        demonnic.chat.windows[demonnic.chat.config.Alltab]:echo(" ")
      end
  end
  demonnic.chat.windows[chat]:append()
  if demonnic.chat.config.gag then 
    deleteLine() 
    tempLineTrigger(1,1, [[if isPrompt() then deleteLine() end]])
  end
  if demonnic.chat.config.Alltab then appendBuffer(string.format("win%s", demonnic.chat.config.Alltab)) end
  if demonnic.chat.config.blink and chat ~= demonnic.chat.currentTab then 
    if (demonnic.chat.config.Alltab == demonnic.chat.currentTab) and not demonnic.chat.config.blinkOnAll then
      return
    else
      demonnic.chat.tabsToBlink[chat] = true
    end
  end
end



function demonnic.chat:blink()
  if demonnic.chat.blinkID then killTimer(demonnic.chat.blinkID) end
  if not demonnic.chat.config.blink then 
    demonnic.chat.blinkTimerOn = false
    return 
  end
  if not demonnic.chat.container.hidden then
    for tab,_ in pairs(demonnic.chat.tabsToBlink) do
      demonnic.chat.tabs[tab]:flash()
    end
  end
  demonnic.chat.blinkID = tempTimer(demonnic.chat.config.blinkTime, function () demonnic.chat:blink() end)
end

function demonnic.chat:topright()
  return {
    fontSize = demonnic.chat.config.fontSize,
    x=string.format("-%sc",demonnic.chat.config.width + 2),
    y=0,
    width="-15px",
    height=string.format("%ic", demonnic.chat.config.lines + 2),
  }
end

function demonnic.chat:topleft()
  return {
    fontSize = demonnic.chat.config.fontSize,
    x=0,
    y=0,
    width=string.format("%sc",demonnic.chat.config.width),
    height=string.format("%ic", demonnic.chat.config.lines + 2),
  }
end

function demonnic.chat:bottomright()
  return {
    fontSize = demonnic.chat.config.fontSize,
    x=string.format("-%sc",demonnic.chat.config.width + 2),
    y=string.format("-%sc",demonnic.chat.config.lines + 2),
    width="-15px",
    height=string.format("%ic", demonnic.chat.config.lines + 2),
  }
end

function demonnic.chat:bottomleft()
  return {
    fontSize = demonnic.chat.config.fontSize,
    x=0,
    y=string.format("-%sc",demonnic.chat.config.lines + 2),
    width=string.format("%sc",demonnic.chat.config.width),
    height=string.format("%ic", demonnic.chat.config.lines + 2),
  }
end</script>
							<eventHandlerList />
						</Script>
						<Script isActive="yes" isFolder="no">
							<name>demonnicOnStart</name>
							<packageName></packageName>
							<script>function demonnicOnStart()
  if demonnic.chat.use then
    demonnic.chat:create()
  end
end</script>
							<eventHandlerList>
								<string>sysLoadEvent</string>
							</eventHandlerList>
						</Script>
						<Script isActive="yes" isFolder="no">
							<name>echo functions</name>
							<packageName></packageName>
							<script>
function demonnic.chat:cecho(chat, message)
  local alltab = demonnic.chat.config.Alltab
  local blink = demonnic.chat.config.blink
  cecho(string.format("win%s",chat), message)
  if alltab and chat ~= alltab then 
    cecho(string.format("win%s", alltab), message)
  end
  if blink and chat ~= demonnic.chat.currentTab then
    if (alltab == demonnic.chat.currentTab) and not demonnic.chat.config.blinkOnAll then
      return
    else
      demonnic.chat.tabsToBlink[chat] = true
    end
  end
end

function demonnic.chat:decho(chat, message)
  local alltab = demonnic.chat.config.Alltab
  local blink = demonnic.chat.config.blink
  decho(string.format("win%s",chat), message)
  if alltab and chat ~= alltab then 
    decho(string.format("win%s", alltab), message)
  end
  if blink and chat ~= demonnic.chat.currentTab then
    if (alltab == demonnic.chat.currentTab) and not demonnic.chat.config.blinkOnAll then
      return
    else
      demonnic.chat.tabsToBlink[chat] = true
    end
  end
end

function demonnic.chat:hecho(chat, message)
  local alltab = demonnic.chat.config.Alltab
  local blink = demonnic.chat.config.blink
  hecho(string.format("win%s",chat), message)
  if alltab and chat ~= alltab then 
    hecho(string.format("win%s", alltab), message)
  end
  if blink and chat ~= demonnic.chat.currentTab then
    if (alltab == demonnic.chat.currentTab) and not demonnic.chat.config.blinkOnAll then
      return
    else
      demonnic.chat.tabsToBlink[chat] = true
    end
  end
end

function demonnic.chat:echo(chat, message)
  local alltab = demonnic.chat.config.Alltab
  local blink = demonnic.chat.config.blink
  echo(string.format("win%s",chat), message)
  if alltab and chat ~= alltab then 
    echo(string.format("win%s", alltab), message)
  end
  if blink and chat ~= demonnic.chat.currentTab then
    if (alltab == demonnic.chat.currentTab) and not demonnic.chat.config.blinkOnAll then
      return
    else
      demonnic.chat.tabsToBlink[chat] = true
    end
  end
end</script>
							<eventHandlerList />
						</Script>
						<Script isActive="yes" isFolder="no">
							<name>demonnicOnInstall</name>
							<packageName></packageName>
							<script>function demonnicOnInstall(_, package)
  if package:find("YATCO") then
  	demonnicOnStart()
  end
end</script>
							<eventHandlerList>
								<string>sysInstall</string>
							</eventHandlerList>
						</Script>
					</ScriptGroup>
				</ScriptGroup>
			</ScriptGroup>
			<Script isActive="no" isFolder="no">
				<name>Changelog</name>
				<packageName></packageName>
				<script>--Changelog

--[[


   1.) Changed the following to support moving chatbox to bottom:
	   
	   -Added GUI.Bottom to Create Background Script
	   -Changed bottom border in Set Borders Script from setBorderBottom(0) to setBorderBottom(h / 4)
	   -Now Utilizing GUI.Box2 fot chatbox at bottom.
	   -Rewrote createFrame() function to allow for automatic frame generating regardless of
		  box size. Using the function is no different.
	   -Adjusted chatContainer height to fit frame nicely.
	   -Adjusted chatContainer x and y to fit frame nicely.
	   -Changed GUI.chatContainer name from GUI.Box2 to GUI.chatContainer
		
			 
	 2.) Added the following to support adding buttons to Box3
	     
		 -Created a new Script named Button which handles buttons individually by row.
		 -Creating button drawing functions so that they will each be called in an init
		  alias to generate them row by row. This will make it easier to change buttons
		  in the future.
		 -Making Box3 smaller. We don't need as many buttons as we have room for and I
		  have ideas for new things to place below Box3!
		 -Box 5 and 6 created. 5 to be used for Legend/Room Info. 6 to be used for skill
		  cooldown icons.
		 -Buttons added in Buttons script. Callbacks there as well. Legend is a toggle and
		  Mudlet/ASCII are separate callbacks.
	     -Legend and room info script created in Room Info/Legend script and proper event
		  handlers added to config for room info.
		 -Added check to Capture Room Map trigger to only display ASCII map if 
		  GUI.buttonWindow.mudletOrAscii == "ASCII". 
				
				
	 3.) Changed the following to support release this week.
	   
	     -Commenting out box 6 draw frame function.
				
					 
	 4.) Various Bug Fixes:
	     
	     -Fixed the trigger for Group chat to capture others talking in group.
         -Fixed gauges so they can no longer overfill into the main buffer by adding:
       
          if pct_health &gt; 100 then
            GUI.Health:setValue(100, 100)
          else
            GUI.Health:setValue(pct_health, 100)
          end
  
          *Equivalent added for each gauge. May look to add an effect to show a bar
           is overfilled when an enemy rages in the future*

  
	 5.) Quality of life:
	     
		 -Added a function to delete a line and the following prompt. It can be
		  utilized in any trigger to delete any line and the prompt it would have
		  created, but I created it primarily for the chatbox. Function is called
		  using deleteLineP().
		 -Added toggles that are saved to a .lua on exit and loaded on load event.
		  Currently they're used to determine if you're shown in the group or not
		  and if you would like to gag chat from main buffer with the above
		  function. (deleteLineP())
			 
	     -

]]
</script>
				<eventHandlerList />
			</Script>
		</ScriptGroup>
	</ScriptPackage>
	<KeyPackage />
	<VariablePackage>
		<HiddenVariables />
	</VariablePackage>
</MudletPackage>
